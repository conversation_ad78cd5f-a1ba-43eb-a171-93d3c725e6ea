@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-weight: 300 700;
  src:
    url('/fonts/helvetica-light.ttf') format('truetype') font-weight(300),
    url('/fonts/helvetica-bold.ttf') format('truetype') font-weight(700);
}

@layer base {
  :root {
    --radius: 0.5rem;

    /* Responsive Breakpoints */
    --breakpoint-sm: 767px;
    --breakpoint-md: 1024px;
    --breakpoint-lg: 1440px;
    --breakpoint-xl: 1920px;

    --navigation-menu-viewport-height: 390px;
    --navigation-menu-viewport-width: 900px;
  }

  body {
    color: theme(colors.foreground.500);
    background: theme(colors.background);
    font-family: theme(fontFamily.body), serif;
    height: 100vh;
    max-height: 100dvh;
    overscroll-behavior: none;
    -webkit-font-smoothing: antialiased;
    overflow: auto;
    text-wrap: pretty;
    scroll-behavior: smooth;
  }

  main:not(.rolex) :where(h1, h2, h3, h4, h5, h6),
  main:not(.rolex) .prose :is(h1, h2, h3, h4, h5, h6) {
    font-family: theme(fontFamily.heading), serif;
    font-weight: 400;
    color: theme(textColor.accent);
    text-wrap: balance;
  }

  main.page :is(h1, h2, h3, h4, h5, h6) {
    font-family: theme(fontFamily.heading), serif;
    color: black;
  }

  h1 {
    font-size: theme(fontSize.4xl);
  }

  section {
    /* padding: 2rem 0; */
    scroll-margin-top: 3rem;

    &:first-child {
      padding-top: 0;
    }

    @media (min-width: 640px) {
      /* padding: 4rem 0; */
    }
  }

  .rolex {
    color: theme(colors.rolex.brown) !important;
    background-color: theme(colors.rolex.beige.400);
    font-family: theme(fontFamily.rolex), sans-serif;
    font-size: theme(fontSize.xl);

    & section:not(:has(.prose)),
    & section .prose *:not(img, iframe:parent) {
      @apply px-4;
    }

    & :where(h1, h2, h3, h4, h5, h6, .prose :is(h1, h2, h3, h4, h5, h6)) {
      font-weight: 700;
      color: theme(textColor.rolex.brown);
    }

    & :where(.prose, p) {
      font-weight: 300;

      & :where(strong, b) {
        font-weight: 700;
      }
    }

    & .prose-rolex {
      padding-top: 30px;
      @media (min-width: 768px) and (max-width: 1023px) {
        padding-top: 30px;
      }
      @media (min-width: 1024px) and (max-width: 1439px) {
        padding-top: 60px;
      }
      @media (min-width: 1440px) {
        padding-top: 90px;
      }

      h1 {
        color: var(--rolex-text-50);
        font-weight: bold;
      }
      .rich-tex-h1:not(.rich-tex-h1 + .rich-tex-h1) {
        @apply mb-0;
      }
      .rich-tex-h1 + .rich-tex-h1 {
        @apply mb-5;
      }

      ul {
        padding-left: 3.5rem;
        padding-right: 1rem;
      }
    }
  }
}

@layer components {
  .btn,
  button {
    display: inline-block;
    padding: 10px 25px;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 9999px;
    transition: all 0.3s ease-in-out;

    &.rolex-primary {
      color: white;
      font-size: 16px;
      height: 44px;
      line-height: 44px;
      letter-spacing: unset;
      padding: 0 30px;
      font-weight: 700;
      background-color: theme(colors.primary);
      &:hover {
        background-color: transparent;
        box-shadow: inset 0 0 0 1px theme(colors.primary);
        color: theme(colors.primary);
      }
    }

    &.rolex-round {
      background-color: rgb(74, 74, 74);
      color: theme(colors.rolex.brown);
      height: 35px;
      width: 35px;
      border-radius: 50%;
      background-position: center;
      background-size: 40%;
      background-repeat: no-repeat;
      position: relative;

      & svg {
        position: absolute;
        inset: 50% auto auto 50%;
        transform: translate(-50%, -50%);
        height: 50%;
      }

      &.lg {
        padding: 1rem;
      }

      &:hover {
        color: theme(colors.rolex.green.500);
      }
    }

    &.rolex-icon {
      background: transparent;
      min-width: auto;
    }

    &.rolex-label {
      color: theme(colors.rolex.green.500);
      display: flex;
      align-items: center;
      font-weight: bold;
      padding: 0;
      margin: 0;

      &::after {
        content: url('/icons/right-arrow.svg');
        inline-size: 12px;
        block-size: 12px;
        display: inline-block;
        margin-inline-start: 0.2rem;
        transition: transform 0.3s ease-in-out;
      }
    }

    &.rolex-dashes {
      height: 0.35rem;
      max-height: 0.35rem !important;
      padding: 0;
      border-radius: 1rem;
      position: relative;
      opacity: 1;
      transition: width 0.2s ease-in-out;
    }
  }

  :has(> button.rolex-label:hover)::after {
    transform: translateX(3px);
  }
}

/* Rolex Utility Classes */
@layer utilities {
  /* Desktop styles */
  @media (min-width: 1024px), (min-width: 1440px), (min-width: 1920px) {
    .rolex-text-70 {
      font-size: clamp(46px, 4vw, 70px);
      line-height: 1.1;
    }
    .rolex-text-50 {
      font-size: clamp(36px, 3vw, 50px);
      line-height: 1.2;
    }
    .rolex-text-36 {
      font-size: clamp(28px, 2.5vw, 36px);
      line-height: 1.2;
    }
    .rolex-text-30 {
      font-size: clamp(23px, 2vw, 30px);
      line-height: 1.2;
    }
    .rolex-text-26 {
      font-size: clamp(23px, 1.8vw, 26px);
      line-height: 1.2;
    }
    .rolex-text-24 {
      font-size: clamp(20px, 1.6vw, 24px);
      line-height: 1.2;
    }
    . {
      font-size: clamp(19px, 1.4vw, 20px);
      line-height: 1.6;
    }
    .rolex-text-16 {
      font-size: clamp(13px, 1.1vw, 16px);
      line-height: 1.1;
    }
    .rolex-text-14 {
      font-size: clamp(11px, 0.9vw, 14px);
      line-height: 1.1;
    }
  }

  /* Base mobile styles */
  .rolex-text-70 {
    font-size: 36px;
    line-height: 1.1;
  }
  .rolex-text-50 {
    font-size: 30px;
    line-height: 1.2;
  }
  .rolex-text-36 {
    font-size: 24px;
    line-height: 1.2;
  }
  .rolex-text-30 {
    font-size: 22px;
    line-height: 1.2;
  }
  .rolex-text-26 {
    font-size: 22px;
    line-height: 1.2;
  }
  .rolex-text-24 {
    font-size: 18px;
    line-height: 1.2;
  }
  . {
    font-size: 18px;
    line-height: 1.6;
  }
  .rolex-text-16 {
    font-size: 12px;
    line-height: 1.1;
  }
  .rolex-text-14 {
    font-size: 10px;
    line-height: 1.1;
  }

  /* Base Mobile Styles (0 - 767px) */
  .headline70 {
    font-size: 36px;
    line-height: 1.1;
    font-weight: 700;
  }

  .headline50 {
    font-size: 30px;
    line-height: 1.2;
    font-weight: 700;
  }

  .headline36 {
    font-size: 24px;
    line-height: 1.2;
    font-weight: 700;
  }

  .headline30 {
    font-size: 22px;
    line-height: 1.2;
    font-weight: 700;
  }

  .headline26 {
    font-size: 22px;
    line-height: 1.2;
    font-weight: 700;
  }

  .body24-bold {
    font-size: 16px;
    line-height: 1.2;
    font-weight: 700;
  }

  .body24-light {
    font-size: 16px;
    line-height: 1.2;
    font-weight: 300;
  }

  .body20-bold {
    font-size: 18px;
    line-height: 1.6;
    font-weight: 700;
  }

  .body20-light {
    font-size: 18px;
    line-height: 1.6;
    font-weight: 300;
  }

  .keepwatch20-bold {
    font-size: 18px;
    line-height: 1.2;
    font-weight: 700;
  }

  .legend18-bold {
    font-size: 12px;
    line-height: 1.1;
    font-weight: 700;
  }

  .legend16-light {
    font-size: 12px;
    line-height: 1.1;
    font-weight: 300;
  }

  .legend14-bold {
    font-size: 10px;
    line-height: 1.1;
    font-weight: 700;
  }

  .fixed22 {
    font-size: 22px;
    line-height: 1.6;
    font-weight: 700;
  }

  .fixed16 {
    font-size: 16px;
    line-height: 1.1;
    font-weight: 700;
  }

  .fixed14 {
    font-size: 14px;
    line-height: 1.1;
    font-weight: 700;
  }

  .quote50 {
    font-size: 32px;
    line-height: 1.3;
    font-weight: 400;
    font-family: 'Georgia';
  }

  /* Tablet Styles (768px - 1023px) */
  @media (min-width: 768px) and (max-width: 1023px) {
    .headline70 {
      font-size: 46px;
    }
    .headline50 {
      font-size: 36px;
    }
    .headline36 {
      font-size: 28px;
    }
    .headline30 {
      font-size: 23px;
    }
    .headline26 {
      font-size: 23px;
    }
    .body24-bold {
      font-size: 22px;
    }
    .body24-light {
      font-size: 22px;
    }
    .body20-bold {
      font-size: 19px;
    }
    .body20-light {
      font-size: 19px;
    }
    .keepwatch20-bold {
      font-size: 19px;
    }
    .legend18-bold {
      font-size: 15px;
    }
    .legend16-light {
      font-size: 12px;
    }
    .legend14-bold {
      font-size: 13px;
    }
    .quote50 {
      font-size: 40px;
    }
  }

  /* Desktop Styles (1024px - 1439px) */
  @media (min-width: 1024px) and (max-width: 1439px) {
    .headline70 {
      font-size: 56px;
    }
    .headline50 {
      font-size: 42px;
    }
    .headline36 {
      font-size: 32px;
    }
    .headline30 {
      font-size: 26px;
    }
    .headline26 {
      font-size: 25px;
    }
    .body24-bold {
      font-size: 22px;
    }
    .body24-light {
      font-size: 22px;
    }
    .body20-bold {
      font-size: 19px;
    }
    .body20-light {
      font-size: 19px;
    }
    .keepwatch20-bold {
      font-size: 19px;
    }
    .legend18-bold {
      font-size: 15px;
    }
    .legend16-light {
      font-size: 15px;
    }
    .legend14-bold {
      font-size: 13px;
    }
    .quote50 {
      font-size: 40px;
    }
  }

  /* Large Desktop Styles (1440px+) */
  @media (min-width: 1440px) {
    .headline70 {
      font-size: 70px;
    }
    .headline50 {
      font-size: 50px;
    }
    .headline36 {
      font-size: 36px;
    }
    .headline30 {
      font-size: 30px;
    }
    .headline26 {
      font-size: 26px;
    }
    .body24-bold {
      font-size: 24px;
    }
    .body24-light {
      font-size: 24px;
    }
    .body20-bold {
      font-size: 20px;
    }
    .body20-light {
      font-size: 20px;
    }
    .keepwatch20-bold {
      font-size: 20px;
    }
    .legend18-bold {
      font-size: 16px;
    }
    .legend16-light {
      font-size: 16px;
    }
    .legend14-bold {
      font-size: 14px;
    }
    .quote50 {
      font-size: 50px;
    }
  }
}
