[{"name": "jewelryProduct", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "jewelryProduct"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "ean": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "brand": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "collection": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "articleGroup": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "referenceNo": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "stock": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "productGroup": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "productLine": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "specialData": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "specialAttributes": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "jewelryDetails": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"sizeType": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "serviceInterval": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "diamonds": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"carat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "clarity": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "cut": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "cutQuality": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero<PERSON>arousel"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featuredItems"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageCta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageGridCols"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "fullWidthCta"}}, {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "video"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "financialPreAppraisal"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "forDev"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPageTitle"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCardGroup"}}, {"type": "object", "attributes": {"heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichTextCalibre"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexHomeBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexWatchGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPostGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexBookingType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "richText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexModelHero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexFeatureList"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactUsAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexProductList"}}]}}, "optional": true}}}, {"name": "watchProduct", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "watchProduct"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "ean": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "brand": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "collection": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "articleGroup": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, "optional": true}, "referenceNo": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "stock": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "productGroup": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "productLine": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "specialData": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "specialAttributes": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "watchDetails": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"strapType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "strapColour": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "caseWidth": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "caseShape": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "caseDepth": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "caseMaterial": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "glassType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "dial": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "dialColor": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "movement": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "claspType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "waterproofRating": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "warranty": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "packaging": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sku": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "isDatum": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isGangreserve": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isMondphase": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isSekunde": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isTagDatum": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isTaucher": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isZweiteZeitZone": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero<PERSON>arousel"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featuredItems"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageCta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageGridCols"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "fullWidthCta"}}, {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "video"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "financialPreAppraisal"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "forDev"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPageTitle"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCardGroup"}}, {"type": "object", "attributes": {"heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichTextCalibre"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexHomeBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexWatchGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPostGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexBookingType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "richText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexModelHero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexFeatureList"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactUsAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexProductList"}}]}}, "optional": true}}}, {"name": "rolexProductList", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexProductList"}}, "category": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "rolexPostGroup", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexPostGroup"}}, "isVisible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexPost", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexPost"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "publishDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "rolexHeaderImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rolexHeaderImageMobile": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "mobileImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero<PERSON>arousel"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featuredItems"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageCta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageGridCols"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "fullWidthCta"}}, {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "video"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "financialPreAppraisal"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "forDev"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPageTitle"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCardGroup"}}, {"type": "object", "attributes": {"heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichTextCalibre"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexHomeBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexWatchGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPostGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexBookingType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "richText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexModelHero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexFeatureList"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactUsAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexProductList"}}]}}, "optional": true}}}, {"name": "rolexWatchGroup", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexWatchGroup"}}, "uiType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "newWatches"}, {"type": "string", "value": "seriesWatch"}, {"type": "string", "value": "banner"}, {"type": "string", "value": "post"}, {"type": "string", "value": "booking"}, {"type": "string", "value": "twoColumns"}, {"type": "string", "value": "multiColumns"}]}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "rolexWatch", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexWatch"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "rmc": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ranking": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "releaseDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "type": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "material": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "modelCase": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "retailPrices": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "rolexHeaderImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rolexHeaderImageMobile": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "category": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "classic"}, {"type": "string", "value": "professional"}, {"type": "string", "value": "perpetual"}]}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "parentWatch": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "rolexWatch"}, "optional": true}, "relatedWatches": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "rolexWatch", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero<PERSON>arousel"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featuredItems"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageCta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageGridCols"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "fullWidthCta"}}, {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "video"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "financialPreAppraisal"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "forDev"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPageTitle"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCardGroup"}}, {"type": "object", "attributes": {"heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichTextCalibre"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexHomeBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexWatchGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPostGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexBookingType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "richText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexModelHero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexFeatureList"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactUsAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexProductList"}}]}}, "optional": true}}}, {"name": "rolexContactUsAccordionType", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexContactUsAccordionType"}}, "visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexAccordionType", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexAccordionType"}}, "visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexFeatureList", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexFeatureList"}}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "cta": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"link": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}}, {"name": "rolexModelHero", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexModelHero"}}, "heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "subheading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "body": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}}}}, {"name": "rolexContactForm", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexContactForm"}}, "visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexContactCard", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexContactCard"}}, "visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexRichTextCalibre", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexRichTextCalibre"}}, "alternativeBackgroundColor": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "modelAvailability": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "button"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"leftImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rightImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageGrid"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "widthRatio": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "5/6"}, {"type": "string", "value": "1/2"}]}, "optional": true}, "centerImage": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "inlineImage"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "unconstrained": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "fullWidthImage"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"quote": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "author": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "year": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "quoteBlock"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}}}}, {"name": "rolexRichText", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexRichText"}}, "alternativeBackgroundColor": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "button"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"leftImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rightImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageGrid"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "explicitWidth": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "explicitHeight": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "centerImage": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "inlineImage"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "unconstrained": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "fullWidthImage"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"quote": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "author": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "year": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "quoteBlock"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}}}}, {"name": "rolexHomeBanner", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexHomeBanner"}}, "isVisible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "rolexExploreCarousel", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "rolexCardGroup", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexCardGroup"}}, "uiType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "newWatches"}, {"type": "string", "value": "seriesWatch"}, {"type": "string", "value": "banner"}, {"type": "string", "value": "post"}, {"type": "string", "value": "booking"}, {"type": "string", "value": "twoColumns"}, {"type": "string", "value": "multiColumns"}]}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "cards": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}}, "optional": true}}}}, {"name": "rolexBookingType", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexBookingType"}}, "visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "cards": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}}, "optional": true}}}}, {"name": "rolexPageTitle", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexPageTitle"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "forDev", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "forDev"}}, "Visible": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "imageGridCols", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageGridCols"}}, "images": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "caption": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "columns": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "number", "value": 1}, {"type": "number", "value": 2}, {"type": "number", "value": 3}, {"type": "number", "value": 4}]}, "optional": true}, "spacing": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "number", "value": 2}, {"type": "number", "value": 4}, {"type": "number", "value": 8}]}, "optional": true}}}}, {"name": "richText", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "richText"}}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"leftImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rightImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageGrid"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}}}}, {"name": "video", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "video"}}, "videoType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "youtube"}, {"type": "string", "value": "file"}]}, "optional": true}, "alternativeBackgroundColor": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "youtubeUrl": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "videoFile": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.fileAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "file"}}}}, "optional": true}}}}, {"name": "contactForm", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "contactForm"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "financialPreAppraisal", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "financialPreAppraisal"}}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "retailPrices": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "number"}}, "optional": true}}}}, {"name": "imageContent", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}}}, {"name": "fullWidthCta", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "fullWidthCta"}}, "backgroundImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaLink": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "<PERSON><PERSON><PERSON><PERSON>"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "subtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "imageCta", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageCta"}}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "featuredItems", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featuredItems"}}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "hero<PERSON>arousel", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "hero<PERSON>arousel"}}, "slides": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "mobileImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hideTitle": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "hideTitleMobile": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isRolexSlide": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "decreasePadding": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}}, {"name": "page", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "page"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ogImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "isProse": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isRolex": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "rolexHeaderImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rolexHeaderImageMobile": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "rolexWatchCta": {"type": "objectAttribute", "value": {"type": "inline", "name": "rolexCard"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "hero<PERSON>arousel"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "featuredItems"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageCta"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "imageGridCols"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "fullWidthCta"}}, {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "dateObjects": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"year": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "imageAlt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "imagePosition": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "left"}, {"type": "string", "value": "right"}]}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "imageContent"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "contactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "video"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "financialPreAppraisal"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "forDev"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPageTitle"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexCardGroup"}}, {"type": "object", "attributes": {"heading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexExploreCarousel"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexRichTextCalibre"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexHomeBanner"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactCard"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactForm"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexWatchGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexPostGroup"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexBookingType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "richText"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexModelHero"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexFeatureList"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexContactUsAccordionType"}}, {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}, "rest": {"type": "inline", "name": "rolexProductList"}}]}}, "optional": true}}}, {"name": "rolexCard", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "rolexCard"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "mobileImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "ctaSubheading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaHeading": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaLink": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uiType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "newWatches"}, {"type": "string", "value": "seriesWatch"}, {"type": "string", "value": "banner"}, {"type": "string", "value": "post"}, {"type": "string", "value": "booking"}, {"type": "string", "value": "twoColumns"}, {"type": "string", "value": "multiColumns"}]}, "optional": true}}}}, {"name": "siteMeta", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "siteMeta"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "brandLogo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "site_name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "ogImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "themeColor": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "isGoogleAnalyticsEnabled": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "googleanalyticsId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "googleSiteVerificationId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "contactInfo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"phone": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "coordinates": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"lat": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "long": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "socialLinks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "platform": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "openingTimes": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"monday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "tuesday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "wednesday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "thursday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "friday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "saturday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sunday": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "address": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]