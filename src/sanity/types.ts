/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type JewelryProduct = {
  _id: string;
  _type: 'jewelryProduct';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  ean?: string;
  title?: string;
  slug?: Slug;
  description?: string;
  brand?: string;
  collection?: string;
  price?: number;
  articleGroup?: {
    name?: string;
    id?: number;
  };
  referenceNo?: string;
  stock?: number;
  productGroup?: string;
  productLine?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  specialData?: Array<{
    name?: string;
    value?: string;
    _key: string;
  }>;
  specialAttributes?: Array<{
    name?: string;
    value?: string;
    _key: string;
  }>;
  jewelryDetails?: {
    sizeType?: number;
    serviceInterval?: number;
    diamonds?: Array<{
      carat?: number;
      clarity?: string;
      color?: string;
      cut?: string;
      cutQuality?: string;
      _key: string;
    }>;
  };
  content?: Array<
    | ({
        _key: string;
      } & ProseTitle)
    | ({
        _key: string;
      } & HeroCarousel)
    | ({
        _key: string;
      } & FeaturedItems)
    | ({
        _key: string;
      } & ImageCta)
    | ({
        _key: string;
      } & ImageGridCols)
    | ({
        _key: string;
      } & FullWidthCta)
    | {
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        _type: 'imageContent';
        _key: string;
      }
    | ({
        _key: string;
      } & ContactForm)
    | ({
        _key: string;
      } & Video)
    | ({
        _key: string;
      } & FinancialPreAppraisal)
    | ({
        _key: string;
      } & ForDev)
    | ({
        _key: string;
      } & RolexPageTitle)
    | ({
        _key: string;
      } & RolexCard)
    | ({
        _key: string;
      } & RolexCardGroup)
    | {
        heading?: string;
        items?: Array<{
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          _key: string;
        }>;
        _type: 'rolexExploreCarousel';
        _key: string;
      }
    | ({
        _key: string;
      } & RolexRichText)
    | ({
        _key: string;
      } & RolexRichTextCalibre)
    | ({
        _key: string;
      } & RolexHomeBanner)
    | ({
        _key: string;
      } & RolexContactCard)
    | ({
        _key: string;
      } & RolexContactForm)
    | ({
        _key: string;
      } & RolexWatchGroup)
    | ({
        _key: string;
      } & RolexPostGroup)
    | ({
        _key: string;
      } & RolexBookingType)
    | ({
        _key: string;
      } & RichText)
    | ({
        _key: string;
      } & RolexModelHero)
    | ({
        _key: string;
      } & RolexFeatureList)
    | ({
        _key: string;
      } & RolexAccordionType)
    | ({
        _key: string;
      } & RolexContactUsAccordionType)
    | ({
        _key: string;
      } & RolexProductList)
  >;
};

export type WatchProduct = {
  _id: string;
  _type: 'watchProduct';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  ean?: string;
  title?: string;
  slug?: Slug;
  description?: string;
  brand?: string;
  collection?: string;
  price?: number;
  articleGroup?: {
    name?: string;
    id?: number;
  };
  referenceNo?: string;
  stock?: number;
  productGroup?: string;
  productLine?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  specialData?: Array<{
    name?: string;
    value?: string;
    _key: string;
  }>;
  specialAttributes?: Array<{
    name?: string;
    value?: string;
    _key: string;
  }>;
  watchDetails?: {
    strapType?: string;
    strapColour?: string;
    caseWidth?: string;
    caseShape?: string;
    caseDepth?: string;
    caseMaterial?: string;
    glassType?: string;
    dial?: string;
    dialColor?: string;
    movement?: string;
    claspType?: string;
    waterproofRating?: number;
    warranty?: string;
    packaging?: string;
    sku?: string;
    isDatum?: boolean;
    isGangreserve?: boolean;
    isMondphase?: boolean;
    isSekunde?: boolean;
    isTagDatum?: boolean;
    isTaucher?: boolean;
    isZweiteZeitZone?: boolean;
  };
  content?: Array<
    | ({
        _key: string;
      } & ProseTitle)
    | ({
        _key: string;
      } & HeroCarousel)
    | ({
        _key: string;
      } & FeaturedItems)
    | ({
        _key: string;
      } & ImageCta)
    | ({
        _key: string;
      } & ImageGridCols)
    | ({
        _key: string;
      } & FullWidthCta)
    | {
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        _type: 'imageContent';
        _key: string;
      }
    | ({
        _key: string;
      } & ContactForm)
    | ({
        _key: string;
      } & Video)
    | ({
        _key: string;
      } & FinancialPreAppraisal)
    | ({
        _key: string;
      } & ForDev)
    | ({
        _key: string;
      } & RolexPageTitle)
    | ({
        _key: string;
      } & RolexCard)
    | ({
        _key: string;
      } & RolexCardGroup)
    | {
        heading?: string;
        items?: Array<{
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          _key: string;
        }>;
        _type: 'rolexExploreCarousel';
        _key: string;
      }
    | ({
        _key: string;
      } & RolexRichText)
    | ({
        _key: string;
      } & RolexRichTextCalibre)
    | ({
        _key: string;
      } & RolexHomeBanner)
    | ({
        _key: string;
      } & RolexContactCard)
    | ({
        _key: string;
      } & RolexContactForm)
    | ({
        _key: string;
      } & RolexWatchGroup)
    | ({
        _key: string;
      } & RolexPostGroup)
    | ({
        _key: string;
      } & RolexBookingType)
    | ({
        _key: string;
      } & RichText)
    | ({
        _key: string;
      } & RolexModelHero)
    | ({
        _key: string;
      } & RolexFeatureList)
    | ({
        _key: string;
      } & RolexAccordionType)
    | ({
        _key: string;
      } & RolexContactUsAccordionType)
    | ({
        _key: string;
      } & RolexProductList)
  >;
};

export type RolexProductList = {
  _type: 'rolexProductList';
  category?: string;
};

export type RolexPostGroup = {
  _type: 'rolexPostGroup';
  isVisible?: boolean;
};

export type RolexPost = {
  _id: string;
  _type: 'rolexPost';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  publishDate?: string;
  slug?: Slug;
  description?: string;
  rolexHeaderImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  rolexHeaderImageMobile?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  mobileImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  content?: Array<
    | ({
        _key: string;
      } & ProseTitle)
    | ({
        _key: string;
      } & HeroCarousel)
    | ({
        _key: string;
      } & FeaturedItems)
    | ({
        _key: string;
      } & ImageCta)
    | ({
        _key: string;
      } & ImageGridCols)
    | ({
        _key: string;
      } & FullWidthCta)
    | {
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        _type: 'imageContent';
        _key: string;
      }
    | ({
        _key: string;
      } & ContactForm)
    | ({
        _key: string;
      } & Video)
    | ({
        _key: string;
      } & FinancialPreAppraisal)
    | ({
        _key: string;
      } & ForDev)
    | ({
        _key: string;
      } & RolexPageTitle)
    | ({
        _key: string;
      } & RolexCard)
    | ({
        _key: string;
      } & RolexCardGroup)
    | {
        heading?: string;
        items?: Array<{
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          _key: string;
        }>;
        _type: 'rolexExploreCarousel';
        _key: string;
      }
    | ({
        _key: string;
      } & RolexRichText)
    | ({
        _key: string;
      } & RolexRichTextCalibre)
    | ({
        _key: string;
      } & RolexHomeBanner)
    | ({
        _key: string;
      } & RolexContactCard)
    | ({
        _key: string;
      } & RolexContactForm)
    | ({
        _key: string;
      } & RolexWatchGroup)
    | ({
        _key: string;
      } & RolexPostGroup)
    | ({
        _key: string;
      } & RolexBookingType)
    | ({
        _key: string;
      } & RichText)
    | ({
        _key: string;
      } & RolexModelHero)
    | ({
        _key: string;
      } & RolexFeatureList)
    | ({
        _key: string;
      } & RolexAccordionType)
    | ({
        _key: string;
      } & RolexContactUsAccordionType)
    | ({
        _key: string;
      } & RolexProductList)
  >;
};

export type RolexWatchGroup = {
  _type: 'rolexWatchGroup';
  uiType?: 'newWatches' | 'seriesWatch' | 'banner' | 'post' | 'booking' | 'twoColumns' | 'multiColumns';
  title?: string;
};

export type RolexWatch = {
  _id: string;
  _type: 'rolexWatch';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  rmc?: string;
  ranking?: number;
  title?: string;
  releaseDate?: string;
  slug?: Slug;
  description?: string;
  type?: string;
  size?: number;
  material?: string;
  modelCase?: string;
  retailPrices?: string;
  rolexHeaderImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  rolexHeaderImageMobile?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  category?: 'classic' | 'professional' | 'perpetual';
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  parentWatch?: {
    _ref: string;
    _type: 'reference';
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: 'rolexWatch';
  };
  relatedWatches?: Array<{
    _ref: string;
    _type: 'reference';
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: 'rolexWatch';
  }>;
  content?: Array<
    | ({
        _key: string;
      } & ProseTitle)
    | ({
        _key: string;
      } & HeroCarousel)
    | ({
        _key: string;
      } & FeaturedItems)
    | ({
        _key: string;
      } & ImageCta)
    | ({
        _key: string;
      } & ImageGridCols)
    | ({
        _key: string;
      } & FullWidthCta)
    | {
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        _type: 'imageContent';
        _key: string;
      }
    | ({
        _key: string;
      } & ContactForm)
    | ({
        _key: string;
      } & Video)
    | ({
        _key: string;
      } & FinancialPreAppraisal)
    | ({
        _key: string;
      } & ForDev)
    | ({
        _key: string;
      } & RolexPageTitle)
    | ({
        _key: string;
      } & RolexCard)
    | ({
        _key: string;
      } & RolexCardGroup)
    | {
        heading?: string;
        items?: Array<{
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          _key: string;
        }>;
        _type: 'rolexExploreCarousel';
        _key: string;
      }
    | ({
        _key: string;
      } & RolexRichText)
    | ({
        _key: string;
      } & RolexRichTextCalibre)
    | ({
        _key: string;
      } & RolexHomeBanner)
    | ({
        _key: string;
      } & RolexContactCard)
    | ({
        _key: string;
      } & RolexContactForm)
    | ({
        _key: string;
      } & RolexWatchGroup)
    | ({
        _key: string;
      } & RolexPostGroup)
    | ({
        _key: string;
      } & RolexBookingType)
    | ({
        _key: string;
      } & RichText)
    | ({
        _key: string;
      } & RolexModelHero)
    | ({
        _key: string;
      } & RolexFeatureList)
    | ({
        _key: string;
      } & RolexAccordionType)
    | ({
        _key: string;
      } & RolexContactUsAccordionType)
    | ({
        _key: string;
      } & RolexProductList)
  >;
};

export type RolexContactUsAccordionType = {
  _type: 'rolexContactUsAccordionType';
  visible?: boolean;
};

export type RolexAccordionType = {
  _type: 'rolexAccordionType';
  visible?: boolean;
};

export type RolexFeatureList = {
  _type: 'rolexFeatureList';
  features?: Array<{
    title?: string;
    description?: string;
    _key: string;
  }>;
  cta?: {
    link?: string;
    text?: string;
  };
};

export type RolexModelHero = {
  _type: 'rolexModelHero';
  heading?: string;
  subheading?: string;
  body?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: 'span';
      _key: string;
    }>;
    style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
    listItem?: 'bullet' | 'number';
    markDefs?: Array<{
      href?: string;
      _type: 'link';
      _key: string;
    }>;
    level?: number;
    _type: 'block';
    _key: string;
  }>;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
};

export type RolexContactForm = {
  _type: 'rolexContactForm';
  visible?: boolean;
};

export type RolexContactCard = {
  _type: 'rolexContactCard';
  visible?: boolean;
};

export type RolexRichTextCalibre = {
  _type: 'rolexRichTextCalibre';
  alternativeBackgroundColor?: boolean;
  modelAvailability?: boolean;
  content?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: 'span';
          _key: string;
        }>;
        style?: 'normal' | 'h1' | 'h2' | 'h3' | 'blockquote';
        listItem?: 'bullet' | 'number';
        markDefs?: Array<
          | {
              href?: string;
              _type: 'link';
              _key: string;
            }
          | {
              href?: string;
              _type: 'button';
              _key: string;
            }
        >;
        level?: number;
        _type: 'block';
        _key: string;
      }
    | {
        leftImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        rightImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        _type: 'imageGrid';
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        widthRatio?: '5/6' | '1/2';
        centerImage?: boolean;
        _type: 'inlineImage';
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        unconstrained?: boolean;
        _type: 'fullWidthImage';
        _key: string;
      }
    | {
        quote?: string;
        author?: string;
        year?: string;
        _type: 'quoteBlock';
        _key: string;
      }
  >;
};

export type RolexRichText = {
  _type: 'rolexRichText';
  alternativeBackgroundColor?: boolean;
  content?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: 'span';
          _key: string;
        }>;
        style?: 'normal' | 'h1' | 'h2' | 'h3' | 'blockquote';
        listItem?: 'bullet' | 'number';
        markDefs?: Array<
          | {
              href?: string;
              _type: 'link';
              _key: string;
            }
          | {
              href?: string;
              _type: 'button';
              _key: string;
            }
        >;
        level?: number;
        _type: 'block';
        _key: string;
      }
    | {
        leftImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        rightImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        _type: 'imageGrid';
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        explicitWidth?: number;
        explicitHeight?: number;
        centerImage?: boolean;
        _type: 'inlineImage';
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        unconstrained?: boolean;
        _type: 'fullWidthImage';
        _key: string;
      }
    | {
        quote?: string;
        author?: string;
        year?: string;
        _type: 'quoteBlock';
        _key: string;
      }
  >;
};

export type RolexHomeBanner = {
  _type: 'rolexHomeBanner';
  isVisible?: boolean;
};

export type RolexExploreCarousel = {
  _id: string;
  _type: 'rolexExploreCarousel';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  heading?: string;
  items?: Array<{
    title?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    };
    href?: string;
    _key: string;
  }>;
};

export type RolexCardGroup = {
  _type: 'rolexCardGroup';
  uiType?: 'newWatches' | 'seriesWatch' | 'banner' | 'post' | 'booking' | 'twoColumns' | 'multiColumns';
  title?: string;
  cards?: Array<
    {
      _key: string;
    } & RolexCard
  >;
};

export type RolexBookingType = {
  _type: 'rolexBookingType';
  visible?: boolean;
  cards?: Array<
    {
      _key: string;
    } & RolexCard
  >;
};

export type RolexPageTitle = {
  _type: 'rolexPageTitle';
  title?: string;
  subtitle?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: 'span';
      _key: string;
    }>;
    style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
    listItem?: 'bullet' | 'number';
    markDefs?: Array<{
      href?: string;
      _type: 'link';
      _key: string;
    }>;
    level?: number;
    _type: 'block';
    _key: string;
  }>;
};

export type ForDev = {
  _type: 'forDev';
  Visible?: boolean;
};

export type ImageGridCols = {
  _type: 'imageGridCols';
  images?: Array<{
    image?: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    };
    alt?: string;
    caption?: string;
    _key: string;
  }>;
  columns?: 1 | 2 | 3 | 4;
  spacing?: 2 | 4 | 8;
};

export type RichText = {
  _type: 'richText';
  content?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: 'span';
          _key: string;
        }>;
        style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
        listItem?: 'bullet' | 'number';
        markDefs?: Array<{
          href?: string;
          _type: 'link';
          _key: string;
        }>;
        level?: number;
        _type: 'block';
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: 'image';
        _key: string;
      }
    | {
        leftImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        rightImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        _type: 'imageGrid';
        _key: string;
      }
  >;
};

export type Video = {
  _type: 'video';
  videoType?: 'youtube' | 'file';
  alternativeBackgroundColor?: boolean;
  youtubeUrl?: string;
  videoFile?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.fileAsset';
    };
    media?: unknown;
    _type: 'file';
  };
};

export type ContactForm = {
  _type: 'contactForm';
  title?: string;
  subtitle?: string;
};

export type FinancialPreAppraisal = {
  _type: 'financialPreAppraisal';
  subtitle?: string;
  retailPrices?: Array<number>;
};

export type ImageContent = {
  _id: string;
  _type: 'imageContent';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: 'span';
      _key: string;
    }>;
    style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
    listItem?: 'bullet' | 'number';
    markDefs?: Array<{
      href?: string;
      _type: 'link';
      _key: string;
    }>;
    level?: number;
    _type: 'block';
    _key: string;
  }>;
  dateObjects?: Array<{
    year?: number;
    description?: string;
    _key: string;
  }>;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  imageAlt?: string;
  imagePosition?: 'left' | 'right';
};

export type FullWidthCta = {
  _type: 'fullWidthCta';
  backgroundImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  title?: string;
  description?: string;
  ctaLink?: string;
  ctaText?: string;
};

export type ProseTitle = {
  _type: 'proseTitle';
  title?: string;
  subtitle?: string;
};

export type ImageCta = {
  _type: 'imageCta';
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  imageAlt?: string;
  title?: string;
  description?: string;
  href?: string;
  text?: string;
};

export type FeaturedItems = {
  _type: 'featuredItems';
  items?: Array<{
    title?: string;
    href?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    };
    _key: string;
  }>;
};

export type HeroCarousel = {
  _type: 'heroCarousel';
  slides?: Array<{
    image?: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    };
    mobileImage?: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    };
    alt?: string;
    href?: string;
    hideTitle?: boolean;
    hideTitleMobile?: boolean;
    isRolexSlide?: boolean;
    title?: string;
    description?: string;
    decreasePadding?: boolean;
    _key: string;
  }>;
};

export type Page = {
  _id: string;
  _type: 'page';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  ogImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  isProse?: boolean;
  isRolex?: boolean;
  rolexHeaderImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  rolexHeaderImageMobile?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  rolexWatchCta?: RolexCard;
  content?: Array<
    | ({
        _key: string;
      } & ProseTitle)
    | ({
        _key: string;
      } & HeroCarousel)
    | ({
        _key: string;
      } & FeaturedItems)
    | ({
        _key: string;
      } & ImageCta)
    | ({
        _key: string;
      } & ImageGridCols)
    | ({
        _key: string;
      } & FullWidthCta)
    | {
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        _type: 'imageContent';
        _key: string;
      }
    | ({
        _key: string;
      } & ContactForm)
    | ({
        _key: string;
      } & Video)
    | ({
        _key: string;
      } & FinancialPreAppraisal)
    | ({
        _key: string;
      } & ForDev)
    | ({
        _key: string;
      } & RolexPageTitle)
    | ({
        _key: string;
      } & RolexCard)
    | ({
        _key: string;
      } & RolexCardGroup)
    | {
        heading?: string;
        items?: Array<{
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          _key: string;
        }>;
        _type: 'rolexExploreCarousel';
        _key: string;
      }
    | ({
        _key: string;
      } & RolexRichText)
    | ({
        _key: string;
      } & RolexRichTextCalibre)
    | ({
        _key: string;
      } & RolexHomeBanner)
    | ({
        _key: string;
      } & RolexContactCard)
    | ({
        _key: string;
      } & RolexContactForm)
    | ({
        _key: string;
      } & RolexWatchGroup)
    | ({
        _key: string;
      } & RolexPostGroup)
    | ({
        _key: string;
      } & RolexBookingType)
    | ({
        _key: string;
      } & RichText)
    | ({
        _key: string;
      } & RolexModelHero)
    | ({
        _key: string;
      } & RolexFeatureList)
    | ({
        _key: string;
      } & RolexAccordionType)
    | ({
        _key: string;
      } & RolexContactUsAccordionType)
    | ({
        _key: string;
      } & RolexProductList)
  >;
};

export type RolexCard = {
  _type: 'rolexCard';
  title?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  mobileImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  ctaSubheading?: string;
  ctaHeading?: string;
  ctaText?: string;
  ctaLink?: string;
  uiType?: 'newWatches' | 'seriesWatch' | 'banner' | 'post' | 'booking' | 'twoColumns' | 'multiColumns';
};

export type SiteMeta = {
  _id: string;
  _type: 'siteMeta';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  brandLogo?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  site_name?: string;
  url?: string;
  title?: string;
  slug?: Slug;
  ogImage?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  description?: string;
  themeColor?: string;
  isGoogleAnalyticsEnabled?: boolean;
  googleanalyticsId?: string;
  googleSiteVerificationId?: string;
  contactInfo?: {
    phone?: string;
    email?: string;
    coordinates?: {
      lat?: string;
      long?: string;
    };
    socialLinks?: Array<{
      url?: string;
      platform?: string;
      _key: string;
    }>;
    openingTimes?: {
      monday?: string;
      tuesday?: string;
      wednesday?: string;
      thursday?: string;
      friday?: string;
      saturday?: string;
      sunday?: string;
    };
    address?: string;
  };
};

export type SanityImagePaletteSwatch = {
  _type: 'sanity.imagePaletteSwatch';
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: 'sanity.imagePalette';
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: 'sanity.imageDimensions';
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: 'sanity.imageHotspot';
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: 'sanity.imageCrop';
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: 'sanity.fileAsset';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: 'sanity.imageAsset';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: 'sanity.imageMetadata';
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: 'geopoint';
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: 'slug';
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: 'sanity.assetSourceData';
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes =
  | JewelryProduct
  | WatchProduct
  | RolexProductList
  | RolexPostGroup
  | RolexPost
  | RolexWatchGroup
  | RolexWatch
  | RolexContactUsAccordionType
  | RolexAccordionType
  | RolexFeatureList
  | RolexModelHero
  | RolexContactForm
  | RolexContactCard
  | RolexRichTextCalibre
  | RolexRichText
  | RolexHomeBanner
  | RolexExploreCarousel
  | RolexCardGroup
  | RolexBookingType
  | RolexPageTitle
  | ForDev
  | ImageGridCols
  | RichText
  | Video
  | ContactForm
  | FinancialPreAppraisal
  | ImageContent
  | FullWidthCta
  | ProseTitle
  | ImageCta
  | FeaturedItems
  | HeroCarousel
  | Page
  | RolexCard
  | SiteMeta
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityImageHotspot
  | SanityImageCrop
  | SanityFileAsset
  | SanityImageAsset
  | SanityImageMetadata
  | Geopoint
  | Slug
  | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: ./src/sanity/lib/queries.ts
// Variable: settingsQuery
// Query: *[_type == "siteMeta" && slug.current == "site-settings"][0] {   url,   title,   description,   themeColor,   contactInfo {        phone,        email,        socialLinks,        openingTimes,        coordinates {          lat,          long,        },        address,    },   "siteName": site_name,   "brandLogo": {      "url": brandLogo.asset->url,      "width": brandLogo.asset->metadata.dimensions.width,      "height": brandLogo.asset->metadata.dimensions.height,   },   "ogImage": {      "url": ogImage.asset->url,      "width": ogImage.asset->metadata.dimensions.width,      "height": ogImage.asset->metadata.dimensions.height,   },  }
export type SettingsQueryResult = {
  url: string | null;
  title: string | null;
  description: string | null;
  themeColor: string | null;
  contactInfo: {
    phone: string | null;
    email: string | null;
    socialLinks: Array<{
      url?: string;
      platform?: string;
      _key: string;
    }> | null;
    openingTimes: {
      monday?: string;
      tuesday?: string;
      wednesday?: string;
      thursday?: string;
      friday?: string;
      saturday?: string;
      sunday?: string;
    } | null;
    coordinates: {
      lat: string | null;
      long: string | null;
    } | null;
    address: string | null;
  } | null;
  siteName: string | null;
  brandLogo: {
    url: string | null;
    width: number | null;
    height: number | null;
  };
  ogImage: {
    url: string | null;
    width: number | null;
    height: number | null;
  };
} | null;
// Variable: contactInformationQuery
// Query: *[_type == "siteMeta" && slug.current == "site-settings"][0] {    contactInfo {        phone,        email,        openingTimes,        address,        coordinates {          lat,          long,        }    },  }
export type ContactInformationQueryResult = {
  contactInfo: {
    phone: string | null;
    email: string | null;
    openingTimes: {
      monday?: string;
      tuesday?: string;
      wednesday?: string;
      thursday?: string;
      friday?: string;
      saturday?: string;
      sunday?: string;
    } | null;
    address: string | null;
    coordinates: {
      lat: string | null;
      long: string | null;
    } | null;
  } | null;
} | null;
// Variable: watchGroupQuery
// Query: *[_type == "rolexWatch" && category == $category && releaseDate < $cutoffDate && slug.current match "/rolex/watches/*" && !defined(parentWatch)] | order(ranking asc) {  title,  image,  slug,  type,  size,  material,  releaseDate,  ranking}
export type WatchGroupQueryResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  type: string | null;
  size: number | null;
  material: string | null;
  releaseDate: string | null;
  ranking: number | null;
}>;
// Variable: watchCollectionQuery
// Query: *[_type == "rolexWatch" && slug.current == $slug][0] {    relatedWatches[]->{      title,      image,      slug    }  }
export type WatchCollectionQueryResult = {
  relatedWatches: Array<{
    title: string | null;
    image: {
      asset?: {
        _ref: string;
        _type: 'reference';
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: 'image';
    } | null;
    slug: Slug | null;
  }> | null;
} | null;
// Variable: exploringWatchesQuery
// Query: *[_type == "rolexWatch" && releaseDate < $cutoffDate && defined(parentWatch)][0..12] {  title,  image,  slug,  releaseDate}
export type ExploringWatchesQueryResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  releaseDate: string | null;
}>;
// Variable: exploringNewWatchesQuery
// Query: *[_type == "rolexWatch" && releaseDate > $cutoffDate && (title match "The*")][0..12] {  title,  image,  slug,  releaseDate}
export type ExploringNewWatchesQueryResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  releaseDate: string | null;
}>;
// Variable: newWatchesQuery
// Query: *[_type == "rolexWatch" && releaseDate > $cutoffDate && !defined(parentWatch) && defined(ranking)] | order(ranking asc) {  title,  image,  slug,  ranking,  releaseDate}
export type NewWatchesQueryResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  ranking: number | null;
  releaseDate: string | null;
}>;
// Variable: rolexPostsQuery
// Query: *[_type == "rolexPost"] {  title,  image,  mobileImage,  slug,  publishDate,  description}
export type RolexPostsQueryResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  mobileImage: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  publishDate: string | null;
  description: string | null;
}>;
// Variable: pageQuery
// Query: *[(_type == "page" || _type == "rolexWatch"  || _type == "rolexPost") && slug.current == $slug][0] {    title,    description,    isProse,    isRolex,    rolexHeaderImage,    rolexHeaderImageMobile,    content[] {      _type,      _key,      ...,      items[] {        _type,        _key,        ...,        content[] {          _type,          _key,          ...        }      }    },    "ogImage": {      "url": ogImage.asset->url,      "width": ogImage.asset->metadata.dimensions.width,      "height": ogImage.asset->metadata.dimensions.height,    },  }
export type PageQueryResult =
  | {
      title: string | null;
      description: string | null;
      isProse: null;
      isRolex: null;
      rolexHeaderImage: {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: 'image';
      } | null;
      rolexHeaderImageMobile: {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: 'image';
      } | null;
      content: Array<
        | {
            _type: 'contactForm';
            _key: string;
            title?: string;
            subtitle?: string;
            items: null;
          }
        | {
            _type: 'featuredItems';
            _key: string;
            items: Array<{
              _type: null;
              _key: string;
              title?: string;
              href?: string;
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              content: null;
            }> | null;
          }
        | {
            _type: 'financialPreAppraisal';
            _key: string;
            subtitle?: string;
            retailPrices?: Array<number>;
            items: null;
          }
        | {
            _type: 'forDev';
            _key: string;
            Visible?: boolean;
            items: null;
          }
        | {
            _type: 'fullWidthCta';
            _key: string;
            backgroundImage?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            title?: string;
            description?: string;
            ctaLink?: string;
            ctaText?: string;
            items: null;
          }
        | {
            _type: 'heroCarousel';
            _key: string;
            slides?: Array<{
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              mobileImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              alt?: string;
              href?: string;
              hideTitle?: boolean;
              hideTitleMobile?: boolean;
              isRolexSlide?: boolean;
              title?: string;
              description?: string;
              decreasePadding?: boolean;
              _key: string;
            }>;
            items: null;
          }
        | {
            _type: 'imageContent';
            _key: string;
            title?: string;
            content?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            dateObjects?: Array<{
              year?: number;
              description?: string;
              _key: string;
            }>;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            imageAlt?: string;
            imagePosition?: 'left' | 'right';
            items: null;
          }
        | {
            _type: 'imageCta';
            _key: string;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            imageAlt?: string;
            title?: string;
            description?: string;
            href?: string;
            text?: string;
            items: null;
          }
        | {
            _type: 'imageGridCols';
            _key: string;
            images?: Array<{
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              alt?: string;
              caption?: string;
              _key: string;
            }>;
            columns?: 1 | 2 | 3 | 4;
            spacing?: 2 | 4 | 8;
            items: null;
          }
        | {
            _type: 'proseTitle';
            _key: string;
            title?: string;
            subtitle?: string;
            items: null;
          }
        | {
            _type: 'richText';
            _key: string;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<{
                    href?: string;
                    _type: 'link';
                    _key: string;
                  }>;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  _type: 'image';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexAccordionType';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexBookingType';
            _key: string;
            visible?: boolean;
            cards?: Array<
              {
                _key: string;
              } & RolexCard
            >;
            items: null;
          }
        | {
            _type: 'rolexCard';
            _key: string;
            title?: string;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            mobileImage?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            ctaSubheading?: string;
            ctaHeading?: string;
            ctaText?: string;
            ctaLink?: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            items: null;
          }
        | {
            _type: 'rolexCardGroup';
            _key: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            title?: string;
            cards?: Array<
              {
                _key: string;
              } & RolexCard
            >;
            items: null;
          }
        | {
            _type: 'rolexContactCard';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexContactForm';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexContactUsAccordionType';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexExploreCarousel';
            _key: string;
            heading?: string;
            items: Array<{
              _type: null;
              _key: string;
              title?: string;
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              href?: string;
              content: null;
            }> | null;
          }
        | {
            _type: 'rolexFeatureList';
            _key: string;
            features?: Array<{
              title?: string;
              description?: string;
              _key: string;
            }>;
            cta?: {
              link?: string;
              text?: string;
            };
            items: null;
          }
        | {
            _type: 'rolexHomeBanner';
            _key: string;
            isVisible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexModelHero';
            _key: string;
            heading?: string;
            subheading?: string;
            body?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            items: null;
          }
        | {
            _type: 'rolexPageTitle';
            _key: string;
            title?: string;
            subtitle?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            items: null;
          }
        | {
            _type: 'rolexPostGroup';
            _key: string;
            isVisible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexProductList';
            _key: string;
            category?: string;
            items: null;
          }
        | {
            _type: 'rolexRichText';
            _key: string;
            alternativeBackgroundColor?: boolean;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<
                    | {
                        href?: string;
                        _type: 'button';
                        _key: string;
                      }
                    | {
                        href?: string;
                        _type: 'link';
                        _key: string;
                      }
                  >;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  unconstrained?: boolean;
                  _type: 'fullWidthImage';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  explicitWidth?: number;
                  explicitHeight?: number;
                  centerImage?: boolean;
                  _type: 'inlineImage';
                  _key: string;
                }
              | {
                  quote?: string;
                  author?: string;
                  year?: string;
                  _type: 'quoteBlock';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexRichTextCalibre';
            _key: string;
            alternativeBackgroundColor?: boolean;
            modelAvailability?: boolean;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<
                    | {
                        href?: string;
                        _type: 'button';
                        _key: string;
                      }
                    | {
                        href?: string;
                        _type: 'link';
                        _key: string;
                      }
                  >;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  unconstrained?: boolean;
                  _type: 'fullWidthImage';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  widthRatio?: '1/2' | '5/6';
                  centerImage?: boolean;
                  _type: 'inlineImage';
                  _key: string;
                }
              | {
                  quote?: string;
                  author?: string;
                  year?: string;
                  _type: 'quoteBlock';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexWatchGroup';
            _key: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            title?: string;
            items: null;
          }
        | {
            _type: 'video';
            _key: string;
            videoType?: 'file' | 'youtube';
            alternativeBackgroundColor?: boolean;
            youtubeUrl?: string;
            videoFile?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.fileAsset';
              };
              media?: unknown;
              _type: 'file';
            };
            items: null;
          }
      > | null;
      ogImage: {
        url: null;
        width: null;
        height: null;
      };
    }
  | {
      title: string | null;
      description: string | null;
      isProse: boolean | null;
      isRolex: boolean | null;
      rolexHeaderImage: {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: 'image';
      } | null;
      rolexHeaderImageMobile: {
        asset?: {
          _ref: string;
          _type: 'reference';
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: 'image';
      } | null;
      content: Array<
        | {
            _type: 'contactForm';
            _key: string;
            title?: string;
            subtitle?: string;
            items: null;
          }
        | {
            _type: 'featuredItems';
            _key: string;
            items: Array<{
              _type: null;
              _key: string;
              title?: string;
              href?: string;
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              content: null;
            }> | null;
          }
        | {
            _type: 'financialPreAppraisal';
            _key: string;
            subtitle?: string;
            retailPrices?: Array<number>;
            items: null;
          }
        | {
            _type: 'forDev';
            _key: string;
            Visible?: boolean;
            items: null;
          }
        | {
            _type: 'fullWidthCta';
            _key: string;
            backgroundImage?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            title?: string;
            description?: string;
            ctaLink?: string;
            ctaText?: string;
            items: null;
          }
        | {
            _type: 'heroCarousel';
            _key: string;
            slides?: Array<{
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              mobileImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              alt?: string;
              href?: string;
              hideTitle?: boolean;
              hideTitleMobile?: boolean;
              isRolexSlide?: boolean;
              title?: string;
              description?: string;
              decreasePadding?: boolean;
              _key: string;
            }>;
            items: null;
          }
        | {
            _type: 'imageContent';
            _key: string;
            title?: string;
            content?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            dateObjects?: Array<{
              year?: number;
              description?: string;
              _key: string;
            }>;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            imageAlt?: string;
            imagePosition?: 'left' | 'right';
            items: null;
          }
        | {
            _type: 'imageCta';
            _key: string;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            imageAlt?: string;
            title?: string;
            description?: string;
            href?: string;
            text?: string;
            items: null;
          }
        | {
            _type: 'imageGridCols';
            _key: string;
            images?: Array<{
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              alt?: string;
              caption?: string;
              _key: string;
            }>;
            columns?: 1 | 2 | 3 | 4;
            spacing?: 2 | 4 | 8;
            items: null;
          }
        | {
            _type: 'proseTitle';
            _key: string;
            title?: string;
            subtitle?: string;
            items: null;
          }
        | {
            _type: 'richText';
            _key: string;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<{
                    href?: string;
                    _type: 'link';
                    _key: string;
                  }>;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  _type: 'image';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexAccordionType';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexBookingType';
            _key: string;
            visible?: boolean;
            cards?: Array<
              {
                _key: string;
              } & RolexCard
            >;
            items: null;
          }
        | {
            _type: 'rolexCard';
            _key: string;
            title?: string;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            mobileImage?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            ctaSubheading?: string;
            ctaHeading?: string;
            ctaText?: string;
            ctaLink?: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            items: null;
          }
        | {
            _type: 'rolexCardGroup';
            _key: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            title?: string;
            cards?: Array<
              {
                _key: string;
              } & RolexCard
            >;
            items: null;
          }
        | {
            _type: 'rolexContactCard';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexContactForm';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexContactUsAccordionType';
            _key: string;
            visible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexExploreCarousel';
            _key: string;
            heading?: string;
            items: Array<{
              _type: null;
              _key: string;
              title?: string;
              image?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              href?: string;
              content: null;
            }> | null;
          }
        | {
            _type: 'rolexFeatureList';
            _key: string;
            features?: Array<{
              title?: string;
              description?: string;
              _key: string;
            }>;
            cta?: {
              link?: string;
              text?: string;
            };
            items: null;
          }
        | {
            _type: 'rolexHomeBanner';
            _key: string;
            isVisible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexModelHero';
            _key: string;
            heading?: string;
            subheading?: string;
            body?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            image?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
            };
            items: null;
          }
        | {
            _type: 'rolexPageTitle';
            _key: string;
            title?: string;
            subtitle?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }>;
            items: null;
          }
        | {
            _type: 'rolexPostGroup';
            _key: string;
            isVisible?: boolean;
            items: null;
          }
        | {
            _type: 'rolexProductList';
            _key: string;
            category?: string;
            items: null;
          }
        | {
            _type: 'rolexRichText';
            _key: string;
            alternativeBackgroundColor?: boolean;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<
                    | {
                        href?: string;
                        _type: 'button';
                        _key: string;
                      }
                    | {
                        href?: string;
                        _type: 'link';
                        _key: string;
                      }
                  >;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  unconstrained?: boolean;
                  _type: 'fullWidthImage';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  explicitWidth?: number;
                  explicitHeight?: number;
                  centerImage?: boolean;
                  _type: 'inlineImage';
                  _key: string;
                }
              | {
                  quote?: string;
                  author?: string;
                  year?: string;
                  _type: 'quoteBlock';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexRichTextCalibre';
            _key: string;
            alternativeBackgroundColor?: boolean;
            modelAvailability?: boolean;
            content?: Array<
              | {
                  children?: Array<{
                    marks?: Array<string>;
                    text?: string;
                    _type: 'span';
                    _key: string;
                  }>;
                  style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
                  listItem?: 'bullet' | 'number';
                  markDefs?: Array<
                    | {
                        href?: string;
                        _type: 'button';
                        _key: string;
                      }
                    | {
                        href?: string;
                        _type: 'link';
                        _key: string;
                      }
                  >;
                  level?: number;
                  _type: 'block';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  unconstrained?: boolean;
                  _type: 'fullWidthImage';
                  _key: string;
                }
              | {
                  leftImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  rightImage?: {
                    asset?: {
                      _ref: string;
                      _type: 'reference';
                      _weak?: boolean;
                      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                    };
                    media?: unknown;
                    hotspot?: SanityImageHotspot;
                    crop?: SanityImageCrop;
                    _type: 'image';
                  };
                  _type: 'imageGrid';
                  _key: string;
                }
              | {
                  asset?: {
                    _ref: string;
                    _type: 'reference';
                    _weak?: boolean;
                    [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                  };
                  media?: unknown;
                  hotspot?: SanityImageHotspot;
                  crop?: SanityImageCrop;
                  alt?: string;
                  widthRatio?: '1/2' | '5/6';
                  centerImage?: boolean;
                  _type: 'inlineImage';
                  _key: string;
                }
              | {
                  quote?: string;
                  author?: string;
                  year?: string;
                  _type: 'quoteBlock';
                  _key: string;
                }
            >;
            items: null;
          }
        | {
            _type: 'rolexWatchGroup';
            _key: string;
            uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
            title?: string;
            items: null;
          }
        | {
            _type: 'video';
            _key: string;
            videoType?: 'file' | 'youtube';
            alternativeBackgroundColor?: boolean;
            youtubeUrl?: string;
            videoFile?: {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.fileAsset';
              };
              media?: unknown;
              _type: 'file';
            };
            items: null;
          }
      > | null;
      ogImage: {
        url: string | null;
        width: number | null;
        height: number | null;
      };
    }
  | null;
// Variable: specificWatchProductQuery
// Query: *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {    rmc,    title,    image,    slug,    type,    size,    material,    releaseDate,    content[] {      _type,      _key,      ...,      items[] {        _type,        _key,        ...,        content[] {          _type,          _key,          ...        }      }    },  }
export type SpecificWatchProductQueryResult = {
  rmc: string | null;
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
  type: string | null;
  size: number | null;
  material: string | null;
  releaseDate: string | null;
  content: Array<
    | {
        _type: 'contactForm';
        _key: string;
        title?: string;
        subtitle?: string;
        items: null;
      }
    | {
        _type: 'featuredItems';
        _key: string;
        items: Array<{
          _type: null;
          _key: string;
          title?: string;
          href?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          content: null;
        }> | null;
      }
    | {
        _type: 'financialPreAppraisal';
        _key: string;
        subtitle?: string;
        retailPrices?: Array<number>;
        items: null;
      }
    | {
        _type: 'forDev';
        _key: string;
        Visible?: boolean;
        items: null;
      }
    | {
        _type: 'fullWidthCta';
        _key: string;
        backgroundImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        title?: string;
        description?: string;
        ctaLink?: string;
        ctaText?: string;
        items: null;
      }
    | {
        _type: 'heroCarousel';
        _key: string;
        slides?: Array<{
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          mobileImage?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          alt?: string;
          href?: string;
          hideTitle?: boolean;
          hideTitleMobile?: boolean;
          isRolexSlide?: boolean;
          title?: string;
          description?: string;
          decreasePadding?: boolean;
          _key: string;
        }>;
        items: null;
      }
    | {
        _type: 'imageContent';
        _key: string;
        title?: string;
        content?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        dateObjects?: Array<{
          year?: number;
          description?: string;
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        imagePosition?: 'left' | 'right';
        items: null;
      }
    | {
        _type: 'imageCta';
        _key: string;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        imageAlt?: string;
        title?: string;
        description?: string;
        href?: string;
        text?: string;
        items: null;
      }
    | {
        _type: 'imageGridCols';
        _key: string;
        images?: Array<{
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          alt?: string;
          caption?: string;
          _key: string;
        }>;
        columns?: 1 | 2 | 3 | 4;
        spacing?: 2 | 4 | 8;
        items: null;
      }
    | {
        _type: 'proseTitle';
        _key: string;
        title?: string;
        subtitle?: string;
        items: null;
      }
    | {
        _type: 'richText';
        _key: string;
        content?: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<{
                href?: string;
                _type: 'link';
                _key: string;
              }>;
              level?: number;
              _type: 'block';
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: 'image';
              _key: string;
            }
          | {
              leftImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              rightImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              _type: 'imageGrid';
              _key: string;
            }
        >;
        items: null;
      }
    | {
        _type: 'rolexAccordionType';
        _key: string;
        visible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexBookingType';
        _key: string;
        visible?: boolean;
        cards?: Array<
          {
            _key: string;
          } & RolexCard
        >;
        items: null;
      }
    | {
        _type: 'rolexCard';
        _key: string;
        title?: string;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        mobileImage?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        ctaSubheading?: string;
        ctaHeading?: string;
        ctaText?: string;
        ctaLink?: string;
        uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
        items: null;
      }
    | {
        _type: 'rolexCardGroup';
        _key: string;
        uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
        title?: string;
        cards?: Array<
          {
            _key: string;
          } & RolexCard
        >;
        items: null;
      }
    | {
        _type: 'rolexContactCard';
        _key: string;
        visible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexContactForm';
        _key: string;
        visible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexContactUsAccordionType';
        _key: string;
        visible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexExploreCarousel';
        _key: string;
        heading?: string;
        items: Array<{
          _type: null;
          _key: string;
          title?: string;
          image?: {
            asset?: {
              _ref: string;
              _type: 'reference';
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
            };
            media?: unknown;
            hotspot?: SanityImageHotspot;
            crop?: SanityImageCrop;
            _type: 'image';
          };
          href?: string;
          content: null;
        }> | null;
      }
    | {
        _type: 'rolexFeatureList';
        _key: string;
        features?: Array<{
          title?: string;
          description?: string;
          _key: string;
        }>;
        cta?: {
          link?: string;
          text?: string;
        };
        items: null;
      }
    | {
        _type: 'rolexHomeBanner';
        _key: string;
        isVisible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexModelHero';
        _key: string;
        heading?: string;
        subheading?: string;
        body?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        image?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
          };
          media?: unknown;
          hotspot?: SanityImageHotspot;
          crop?: SanityImageCrop;
          _type: 'image';
        };
        items: null;
      }
    | {
        _type: 'rolexPageTitle';
        _key: string;
        title?: string;
        subtitle?: Array<{
          children?: Array<{
            marks?: Array<string>;
            text?: string;
            _type: 'span';
            _key: string;
          }>;
          style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal';
          listItem?: 'bullet' | 'number';
          markDefs?: Array<{
            href?: string;
            _type: 'link';
            _key: string;
          }>;
          level?: number;
          _type: 'block';
          _key: string;
        }>;
        items: null;
      }
    | {
        _type: 'rolexPostGroup';
        _key: string;
        isVisible?: boolean;
        items: null;
      }
    | {
        _type: 'rolexProductList';
        _key: string;
        category?: string;
        items: null;
      }
    | {
        _type: 'rolexRichText';
        _key: string;
        alternativeBackgroundColor?: boolean;
        content?: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<
                | {
                    href?: string;
                    _type: 'button';
                    _key: string;
                  }
                | {
                    href?: string;
                    _type: 'link';
                    _key: string;
                  }
              >;
              level?: number;
              _type: 'block';
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              alt?: string;
              unconstrained?: boolean;
              _type: 'fullWidthImage';
              _key: string;
            }
          | {
              leftImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              rightImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              _type: 'imageGrid';
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              alt?: string;
              explicitWidth?: number;
              explicitHeight?: number;
              centerImage?: boolean;
              _type: 'inlineImage';
              _key: string;
            }
          | {
              quote?: string;
              author?: string;
              year?: string;
              _type: 'quoteBlock';
              _key: string;
            }
        >;
        items: null;
      }
    | {
        _type: 'rolexRichTextCalibre';
        _key: string;
        alternativeBackgroundColor?: boolean;
        modelAvailability?: boolean;
        content?: Array<
          | {
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: 'span';
                _key: string;
              }>;
              style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'normal';
              listItem?: 'bullet' | 'number';
              markDefs?: Array<
                | {
                    href?: string;
                    _type: 'button';
                    _key: string;
                  }
                | {
                    href?: string;
                    _type: 'link';
                    _key: string;
                  }
              >;
              level?: number;
              _type: 'block';
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              alt?: string;
              unconstrained?: boolean;
              _type: 'fullWidthImage';
              _key: string;
            }
          | {
              leftImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              rightImage?: {
                asset?: {
                  _ref: string;
                  _type: 'reference';
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
                };
                media?: unknown;
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: 'image';
              };
              _type: 'imageGrid';
              _key: string;
            }
          | {
              asset?: {
                _ref: string;
                _type: 'reference';
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
              };
              media?: unknown;
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              alt?: string;
              widthRatio?: '1/2' | '5/6';
              centerImage?: boolean;
              _type: 'inlineImage';
              _key: string;
            }
          | {
              quote?: string;
              author?: string;
              year?: string;
              _type: 'quoteBlock';
              _key: string;
            }
        >;
        items: null;
      }
    | {
        _type: 'rolexWatchGroup';
        _key: string;
        uiType?: 'banner' | 'booking' | 'multiColumns' | 'newWatches' | 'post' | 'seriesWatch' | 'twoColumns';
        title?: string;
        items: null;
      }
    | {
        _type: 'video';
        _key: string;
        videoType?: 'file' | 'youtube';
        alternativeBackgroundColor?: boolean;
        youtubeUrl?: string;
        videoFile?: {
          asset?: {
            _ref: string;
            _type: 'reference';
            _weak?: boolean;
            [internalGroqTypeReferenceTo]?: 'sanity.fileAsset';
          };
          media?: unknown;
          _type: 'file';
        };
        items: null;
      }
  > | null;
} | null;
// Variable: specificWatchProductRetailPriceQuery
// Query: *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {    rmc,    retailPrices  }
export type SpecificWatchProductRetailPriceQueryResult = {
  rmc: string | null;
  retailPrices: string | null;
} | null;
// Variable: categoriesQueyr
// Query: *[_type == "rolexWatch" && !defined(parentWatch) && !(title match "The*")] {  title,  image,  slug,}
export type CategoriesQueyrResult = Array<{
  title: string | null;
  image: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  } | null;
  slug: Slug | null;
}>;

// Query TypeMap
import '@sanity/client';
declare module '@sanity/client' {
  interface SanityQueries {
    '\n  *[_type == "siteMeta" && slug.current == "site-settings"][0] {\n   url,\n   title,\n   description,\n   themeColor,\n   contactInfo {\n        phone,\n        email,\n        socialLinks,\n        openingTimes,\n        coordinates {\n          lat,\n          long,\n        },\n        address,\n    },\n   "siteName": site_name,\n   "brandLogo": {\n      "url": brandLogo.asset->url,\n      "width": brandLogo.asset->metadata.dimensions.width,\n      "height": brandLogo.asset->metadata.dimensions.height,\n   },\n   "ogImage": {\n      "url": ogImage.asset->url,\n      "width": ogImage.asset->metadata.dimensions.width,\n      "height": ogImage.asset->metadata.dimensions.height,\n   },\n  }\n': SettingsQueryResult;
    '\n  *[_type == "siteMeta" && slug.current == "site-settings"][0] {\n    contactInfo {\n        phone,\n        email,\n        openingTimes,\n        address,\n        coordinates {\n          lat,\n          long,\n        }\n    },\n  }\n': ContactInformationQueryResult;
    '\n*[_type == "rolexWatch" && category == $category && releaseDate < $cutoffDate && slug.current match "/rolex/watches/*" && !defined(parentWatch)] | order(ranking asc) {\n  title,\n  image,\n  slug,\n  type,\n  size,\n  material,\n  releaseDate,\n  ranking\n}\n': WatchGroupQueryResult;
    '\n  *[_type == "rolexWatch" && slug.current == $slug][0] {\n    relatedWatches[]->{\n      title,\n      image,\n      slug\n    }\n  }\n': WatchCollectionQueryResult;
    '\n*[_type == "rolexWatch" && releaseDate < $cutoffDate && defined(parentWatch)][0..12] {\n  title,\n  image,\n  slug,\n  releaseDate\n}\n': ExploringWatchesQueryResult;
    '\n*[_type == "rolexWatch" && releaseDate > $cutoffDate && (title match "The*")][0..12] {\n  title,\n  image,\n  slug,\n  releaseDate\n}\n': ExploringNewWatchesQueryResult;
    '\n*[_type == "rolexWatch" && releaseDate > $cutoffDate && !defined(parentWatch) && defined(ranking)] | order(ranking asc) {\n  title,\n  image,\n  slug,\n  ranking,\n  releaseDate\n}\n': NewWatchesQueryResult;
    '\n*[_type == "rolexPost"] {\n  title,\n  image,\n  mobileImage,\n  slug,\n  publishDate,\n  description\n}\n': RolexPostsQueryResult;
    '\n  *[(_type == "page" || _type == "rolexWatch"  || _type == "rolexPost") && slug.current == $slug][0] {\n    title,\n    description,\n    isProse,\n    isRolex,\n    rolexHeaderImage,\n    rolexHeaderImageMobile,\n    content[] {\n      _type,\n      _key,\n      ...,\n      items[] {\n        _type,\n        _key,\n        ...,\n        content[] {\n          _type,\n          _key,\n          ...\n        }\n      }\n    },\n    "ogImage": {\n      "url": ogImage.asset->url,\n      "width": ogImage.asset->metadata.dimensions.width,\n      "height": ogImage.asset->metadata.dimensions.height,\n    },\n  }\n': PageQueryResult;
    '\n  *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {\n    rmc,\n    title,\n    image,\n    slug,\n    type,\n    size,\n    material,\n    releaseDate,\n    content[] {\n      _type,\n      _key,\n      ...,\n      items[] {\n        _type,\n        _key,\n        ...,\n        content[] {\n          _type,\n          _key,\n          ...\n        }\n      }\n    },\n  }\n': SpecificWatchProductQueryResult;
    '\n  *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {\n    rmc,\n    retailPrices\n  }\n': SpecificWatchProductRetailPriceQueryResult;
    '\n*[_type == "rolexWatch" && !defined(parentWatch) && !(title match "The*")] {\n  title,\n  image,\n  slug,\n}\n': CategoriesQueyrResult;
  }
}
