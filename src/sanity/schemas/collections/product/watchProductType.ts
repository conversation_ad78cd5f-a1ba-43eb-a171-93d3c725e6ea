import { defineField, defineType } from 'sanity';
import contentType from '../../blocks/contentType';

export default defineType({
  name: 'watchProduct',
  title: 'Watch Product',
  type: 'document',
  groups: [
    {
      name: 'meta',
      title: 'Page Metadata',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
  ],
  fields: [
    defineField({
      name: 'ean', // EAN编码 / 条形码
      title: 'EAN',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'title', // 商品标题
      title: 'Title',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'ean',
        slugify: (input: string) => `watches/${input}`,
      },
      group: 'meta',
    }),
    defineField({
      name: 'description', // 在线商店产品描述（测试）
      title: 'Description',
      type: 'text',
      group: 'meta',
    }),
    defineField({
      name: 'brand', // 品牌
      title: 'Brand',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'collection', // 系列
      title: 'Collection',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'price', // 售价（含税或未含税取决系统设置）
      title: 'Price',
      type: 'number',
      group: 'meta',
    }),
    defineField({
      name: 'articleGroup',
      title: 'Article Group',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' }, // 商品分类名称
        { name: 'id', title: 'ID', type: 'number' }, // 商品分类ID
      ],
      group: 'meta',
    }),
    defineField({
      name: 'referenceNo', // 参考编号（内部或品牌型号）
      title: 'Reference Number',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'stock', // 库存
      title: 'Stock',
      type: 'number',
      group: 'meta',
    }),
    defineField({
      name: 'productGroup', // 产品组
      title: 'Product Group',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'productLine', // 产品线（如：男款）
      title: 'Product Line',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'image',
      title: 'Main Image',
      type: 'image',
      group: 'meta',
    }),
    defineField({
      name: 'specialData', // 特殊字段（展示属性）
      title: 'Special Data',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
      group: 'meta',
    }),
    defineField({
      name: 'specialAttributes', // 特殊属性（当前为空）
      title: 'Special Attributes',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
      group: 'meta',
    }),
    defineField({
      name: 'watchDetails',
      title: 'Watch Details',
      type: 'object',
      group: 'meta',
      fields: [
        { name: 'strapType', title: 'Strap type', type: 'string' }, // 表带类型
        { name: 'strapColour', title: 'Strap Colour', type: 'string' }, // 表带颜色
        { name: 'caseWidth', title: 'Case Width', type: 'string' }, // 外壳宽度
        { name: 'caseShape', title: 'Case Shape', type: 'string' }, // 外壳形状
        { name: 'caseDepth', title: 'Case Depth', type: 'string' }, // 外壳深度
        { name: 'caseMaterial', title: 'Case Material', type: 'string' }, // 外壳材料
        { name: 'glassType', title: 'Glass Type', type: 'string' }, // 玻璃类型
        { name: 'dial', title: 'Dial', type: 'string' }, // 表盘
        { name: 'dialColor', title: 'Dial Color', type: 'string' }, // 表盘颜色
        { name: 'movement', title: 'Movement', type: 'string' }, // 机芯
        { name: 'claspType', title: 'Clasp Type', type: 'string' }, // 卡扣类型
        { name: 'waterproofRating', title: 'Waterproof Rating', type: 'number' }, // 防水等级
        { name: 'warranty', title: 'Warranty', type: 'string' }, // 保修
        { name: 'packaging', title: 'Packaging', type: 'string' }, // 包装
        { name: 'sku', title: 'SKU', type: 'string' }, // 规格
        { name: 'isDatum', title: 'Is Datum', type: 'boolean' }, // 是否有日期显示
        { name: 'isGangreserve', title: 'Is Gangreserve', type: 'boolean' }, // 是否有动力储存显示
        { name: 'isMondphase', title: 'Is Mondphase', type: 'boolean' }, // 是否有月相功能
        { name: 'isSekunde', title: 'Is Sekunde', type: 'boolean' }, // 是否有秒针
        { name: 'isTagDatum', title: 'Is TagDatum', type: 'boolean' }, // 是否有星期和日期显示
        { name: 'isTaucher', title: 'Is Taucher', type: 'boolean' }, // 是否为潜水表
        { name: 'isZweiteZeitZone', title: 'Is ZweiteZeitZone', type: 'boolean' }, // 是否有第二时区功能
      ],
    }),
    contentType,
  ],
});
