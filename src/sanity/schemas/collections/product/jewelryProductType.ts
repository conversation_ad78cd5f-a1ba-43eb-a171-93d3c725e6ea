import { defineField, defineType } from 'sanity';
import contentType from '../../blocks/contentType';

export default defineType({
  name: 'jewelryProduct',
  title: 'Jewelry Product',
  type: 'document',
  groups: [
    {
      name: 'meta',
      title: 'Page Metadata',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
  ],
  fields: [
    defineField({
      name: 'ean',
      title: 'EAN',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'ean',
        slugify: (input: string) => `jewellery/${input}`,
      },
      group: 'meta',
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      group: 'meta',
    }),
    defineField({
      name: 'brand',
      title: 'Brand',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'collection',
      title: 'Collection',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'price',
      title: 'Price',
      type: 'number',
      group: 'meta',
    }),
    defineField({
      name: 'articleGroup',
      title: 'Article Group',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' },
        { name: 'id', title: 'ID', type: 'number' },
      ],
      group: 'meta',
    }),
    defineField({
      name: 'referenceNo',
      title: 'Reference Number',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'stock',
      title: 'Stock',
      type: 'number',
      group: 'meta',
    }),
    defineField({
      name: 'productGroup',
      title: 'Product Group',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'productLine',
      title: 'Product Line',
      type: 'string',
      group: 'meta',
    }),
    defineField({
      name: 'image',
      title: 'Main Image',
      type: 'image',
      group: 'meta',
    }),
    defineField({
      name: 'specialData',
      title: 'Special Data',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
      group: 'meta',
    }),
    defineField({
      name: 'specialAttributes',
      title: 'Special Attributes',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
      group: 'meta',
    }),
    defineField({
      name: 'jewelryDetails',
      title: 'Jewelry Details',
      type: 'object',
      fields: [
        { name: 'sizeType', title: 'Size Type', type: 'number' },
        { name: 'serviceInterval', title: 'Service Interval', type: 'number' },
        {
          name: 'diamonds',
          title: 'Diamonds',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'carat', title: 'Carat', type: 'number' },
                { name: 'clarity', title: 'Clarity', type: 'string' },
                { name: 'color', title: 'Color', type: 'string' },
                { name: 'cut', title: 'Cut', type: 'string' },
                { name: 'cutQuality', title: 'Cut Quality', type: 'string' },
              ],
            },
          ],
        },
      ],
      group: 'meta',
    }),
    contentType,
  ],
});
