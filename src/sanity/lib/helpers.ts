import { adjustYearIfBeforeApril } from '@/lib/utils';
import { client } from '@/sanity/lib/client';
import { cache } from 'react';
import type {
  ContactInformationQueryResult,
  ExploringNewWatchesQueryResult,
  ExploringWatchesQueryResult,
  NewWatchesQueryResult,
  PageQueryResult,
  RolexPostsQueryResult,
  SettingsQueryResult,
  WatchCollectionQueryResult,
  WatchGroupQueryResult,
} from '../types';
import {
  categoriesQueyr,
  exploringNewWatchesQuery,
  exploringWatchesQuery,
  newWatchesQuery,
  pageQuery,
  rolexPostsQuery,
  specificWatchProductQuery,
  specificWatchProductRetailPriceQuery,
  watchCollectionQuery,
  watchGroupQuery,
  watchProductListByCategory,
} from './queries/rolex';

import { settingsQuery, contactInformationQuery } from './queries/site';

// MARK: - Configuration
const CACHE_OPTIONS = {
  next: {
    revalidate: 0,
  },
};

// MARK: - Site Settings & Page Content
export const getSiteSettings = cache(async () => {
  const settingsContent = await client.fetch(settingsQuery, {}, CACHE_OPTIONS);
  return settingsContent as SettingsQueryResult;
});

export const getPageBySlug = cache(async (slug: string) => {
  const pageContent = await client.fetch(pageQuery, { slug }, CACHE_OPTIONS);
  return pageContent as PageQueryResult;
});

export const getContactInformation = cache(async () => {
  const contactInformation = await client.fetch(contactInformationQuery, {}, CACHE_OPTIONS);
  return contactInformation as ContactInformationQueryResult;
});

// MARK: - Watch Collections
export const getWatchGroup = cache(async (category: string, isCollection?: boolean | null) => {
  const year = adjustYearIfBeforeApril(new Date());
  const cutoffDate = `${year}-08-07`;

  if (isCollection) {
    const watchGroup = await client.fetch(
      watchCollectionQuery,
      {
        slug: `/rolex/watches/${category}`,
      },
      CACHE_OPTIONS
    );
    return watchGroup as WatchCollectionQueryResult;
  }

  const watchGroup = await client.fetch(
    watchGroupQuery,
    {
      category,
      cutoffDate,
    },
    CACHE_OPTIONS
  );
  return watchGroup as WatchGroupQueryResult;
});

// MARK: - New Watches
export const getNewWatches = cache(async () => {
  const year = adjustYearIfBeforeApril(new Date());
  const cutoffDate = `${year}-03-31`;

  const newWatches = await client.fetch(
    newWatchesQuery,
    {
      cutoffDate,
    },
    CACHE_OPTIONS
  );

  // const sortOrder: RolexCardProps['title'][] = [
  //   'The Land Dweller',
  //   'The GMT Master II',
  //   'The Oyster Perpetual',
  //   'The 1908',
  //   'The Datejust',
  //   'Exclusive Dials',
  // ];

  // const sortedWatches = [...newWatches].sort(
  //   (a: RolexCardProps, b: RolexCardProps) =>
  //     sortOrder.indexOf(a.title?.trim() as string) - sortOrder.indexOf(b.title?.trim() as string)
  // );

  return newWatches as NewWatchesQueryResult;
});

// MARK: - Exploring Sections
export const getExploringWatches = cache(async () => {
  const year = adjustYearIfBeforeApril(new Date());
  const cutoffDate = `${year}-08-31`;

  const exploringWatches = await client.fetch(
    exploringWatchesQuery,
    {
      cutoffDate,
    },
    CACHE_OPTIONS
  );

  return exploringWatches as ExploringWatchesQueryResult;
});

export const getExploringNewWatches = cache(async () => {
  const year = adjustYearIfBeforeApril(new Date());
  const cutoffDate = `${year}-08-31`;

  const newExploringWatches = await client.fetch(
    exploringNewWatchesQuery,
    {
      cutoffDate,
    },
    CACHE_OPTIONS
  );

  return newExploringWatches as ExploringNewWatchesQueryResult;
});

// MARK: - Rolex Posts
export const getRolexPosts = cache(async () => {
  const rolexPosts = await client.fetch(rolexPostsQuery, {}, CACHE_OPTIONS);

  return rolexPosts as RolexPostsQueryResult;
});

// MARK: - 获取劳力士某个系列的手表，比如 Land Dweller 系列下的手表：Land Dweller 36、Land Dweller 40
export const getWatchProductListByCategory = cache(async (slug: string, page = 1, itemsPerPage: number = 6) => {
  const start = (page - 1) * itemsPerPage;
  // 确保分页查询正确
  const result = await client.fetch(
    watchProductListByCategory(start, itemsPerPage),
    {
      slug,
    },
    CACHE_OPTIONS
  );

  return result;
});

// MARK: - 获取劳力士某个特定型号
export const getSpecificWatchProduct = cache(async (slug: string) => {
  const result = await client.fetch(
    specificWatchProductQuery,
    {
      slug,
    },
    CACHE_OPTIONS
  );

  return result;
});

// MARK: - 获取劳力士某个特定型号的零售价格
export const getSpecificWatchProductRetailPrice = cache(async (slug: string) => {
  const result = await client.fetch(
    specificWatchProductRetailPriceQuery,
    {
      slug,
    },
    CACHE_OPTIONS
  );

  return result;
});

// MARK: - 获取所有分类
export const getAllCategories = cache(async () => {
  const result = await client.fetch(categoriesQueyr, {}, CACHE_OPTIONS);

  return result;
});
