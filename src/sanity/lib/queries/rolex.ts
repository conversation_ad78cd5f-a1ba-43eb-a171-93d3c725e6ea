import { groq } from 'next-sanity';

export const watchGroupQuery = groq`
*[_type == "rolexWatch" && category == $category && releaseDate < $cutoffDate && slug.current match "/rolex/watches/*" && !defined(parentWatch)] | order(ranking asc) {
  title,
  image,
  slug,
  type,
  size,
  material,
  releaseDate,
  ranking
}
`;

export const watchCollectionQuery = groq`
  *[_type == "rolexWatch" && slug.current == $slug][0] {
    relatedWatches[]->{
      title,
      image,
      slug
    }
  }
`;

export const exploringWatchesQuery = groq`
*[_type == "rolexWatch" && releaseDate < $cutoffDate && defined(parentWatch)][0..12] {
  title,
  image,
  slug,
  releaseDate
}
`;

export const exploringNewWatchesQuery = groq`
*[_type == "rolexWatch" && releaseDate > $cutoffDate && (title match "The*")][0..12] {
  title,
  image,
  slug,
  releaseDate
}
`;

export const newWatchesQuery = groq`
*[_type == "rolexWatch" && releaseDate > $cutoffDate && !defined(parentWatch) && defined(ranking)] | order(ranking asc) {
  title,
  image,
  slug,
  ranking,
  releaseDate
}
`;

export const rolexPostsQuery = groq`
*[_type == "rolexPost"] {
  title,
  image,
  mobileImage,
  slug,
  publishDate,
  description
}
`;

export const pageQuery = groq`
  *[(_type == "page" || _type == "rolexWatch"  || _type == "rolexPost") && slug.current == $slug][0] {
    title,
    description,
    isProse,
    isRolex,
    rolexHeaderImage,
    rolexHeaderImageMobile,
    content[] {
      _type,
      _key,
      ...,
      items[] {
        _type,
        _key,
        ...,
        content[] {
          _type,
          _key,
          ...
        }
      }
    },
    "ogImage": {
      "url": ogImage.asset->url,
      "width": ogImage.asset->metadata.dimensions.width,
      "height": ogImage.asset->metadata.dimensions.height,
    },
  }
`;

export const watchProductListByCategory = (start: number, itemsPerPage: number) => groq`
{
  "watches": *[_type == "rolexWatch" && defined(parentWatch) && defined(ranking) && string::startsWith(slug.current, $slug)] | order(ranking asc) [${start}...${start + itemsPerPage}] {
    rmc,
    title,
    image,
    slug,
    modelCase,
    ranking,
    releaseDate,
    content[] {
      _type,
      _key,
      ...,
      items[] {
        _type,
        _key,
        ...,
        content[] {
          _type,
          _key,
          ...
        }
      }
    },
  },
  "total": count(*[_type == "rolexWatch" && defined(parentWatch) && string::startsWith(slug.current, $slug)])
}
`;

/**
 * 查询特定型号商品
 * @returns
 */
export const specificWatchProductQuery = groq`
  *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {
    rmc,
    title,
    image,
    slug,
    type,
    size,
    material,
    releaseDate,
    content[] {
      _type,
      _key,
      ...,
      items[] {
        _type,
        _key,
        ...,
        content[] {
          _type,
          _key,
          ...
        }
      }
    },
  }
`;

/**
 * 查询特定型号商品的零售价格
 * @returns
 */
export const specificWatchProductRetailPriceQuery = groq`
  *[_type == "rolexWatch" && defined(parentWatch) && slug.current == $slug] [0] {
    rmc,
    retailPrices
  }
`;

/**
 * 查询所有分类
 * @returns
 */
export const categoriesQueyr = groq`
*[_type == "rolexWatch" && !defined(parentWatch) && !(title match "The*")] {
  title,
  image,
  slug,
}
`;
