import { groq } from 'next-sanity';

// TODO: look into `defineQuery` instead of `groq`
export const settingsQuery = groq`
  *[_type == "siteMeta" && slug.current == "site-settings"][0] {
   url,
   title,
   description,
   themeColor,
   contactInfo {
        phone,
        email,
        socialLinks,
        openingTimes,
        coordinates {
          lat,
          long,
        },
        address,
    },
   "siteName": site_name,
   "brandLogo": {
      "url": brandLogo.asset->url,
      "width": brandLogo.asset->metadata.dimensions.width,
      "height": brandLogo.asset->metadata.dimensions.height,
   },
   "ogImage": {
      "url": ogImage.asset->url,
      "width": ogImage.asset->metadata.dimensions.width,
      "height": ogImage.asset->metadata.dimensions.height,
   },
  }
`;

export const contactInformationQuery = groq`
  *[_type == "siteMeta" && slug.current == "site-settings"][0] {
    contactInfo {
        phone,
        email,
        openingTimes,
        address,
        coordinates {
          lat,
          long,
        }
    },
  }
`;
