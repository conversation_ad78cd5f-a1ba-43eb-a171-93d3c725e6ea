'use client';

import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { cn } from '@/lib/utils';
import { getAllCategories } from '@/sanity/lib/helpers';
import Autoplay from 'embla-carousel-autoplay';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface ShopAllWatchesProps {
  byCollection: {
    label: string;
    href: string;
    imageSrc: string;
    alt: string;
  }[];
  byBrand: {
    label: string;
    href: string;
    imageSrc: string;
    alt: string;
  }[];
}

export default function ShopAllWatches({ byCollection, byBrand }: ShopAllWatchesProps) {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCount] = useState(0);
  const [filteredItems, setFilteredItems] = useState<
    {
      key: string;
      title: string;
      image: string;
      slug: { current: string };
    }[]
  >([]);

  const pathname = usePathname();

  // Normalize items to a consistent format
  const normalizeItems = (items: any[]) =>
    items.map((item: any) => {
      if (typeof item === 'string') {
        const uri = item
          .toLowerCase()
          .replaceAll(' ', '-')
          .replace(/-\d{4}/, '')
          .replaceAll('rolex-', '');

        return {
          key: item,
          title: item,
          image: `/images/keep-exploring/${uri.includes('discover') ? 'rolex' : uri}.jpg`,
          slug: { current: `/rolex/${uri.includes('discover') ? '' : uri}` },
        };
      } else {
        return {
          key: item.slug.current,
          title: item.title.replace(/Rolex /gi, ''),
          // @ts-expect-error  possible null
          image: urlFor(item.image).url() || '',
          slug: item.slug || '',
        };
      }
    });

  useEffect(() => {
    const fetchFilteredItems = async () => {
      let result: { key: string; title: string; image: string; slug: { current: string } }[] = [];

      const list = await getAllCategories();
      result = normalizeItems(list.filter((item: any) => !item.slug.current.includes('/rolex/new-watches')));
      setFilteredItems(result);
    };

    fetchFilteredItems();
  }, [pathname]);

  const plugin = React.useRef(Autoplay({ delay: 4000, stopOnInteraction: true }));

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);
  byCollection = [
    {
      label: 'Men’s Watches',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: '1',
    },
    {
      label: 'Women’s Watches',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: '1',
    },
    {
      label: 'Latest Arrivals',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: '1',
    },
  ];

  byBrand = [
    {
      label: 'Omega',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: 'Omega',
    },
    {
      label: 'Tag Heuer',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: 'Tag Heuer',
    },
    {
      label: 'Breitling',
      href: '/watches/mens-watches',
      imageSrc: '/images/keep-exploring/browse.png',
      alt: 'Breitling',
    },
  ];
  return (
    <div className="flex w-full flex-col justify-center bg-white">
      <div className="mx-auto mt-[60px] flex flex-wrap gap-x-[30px]">
        {byCollection.map((item, index) => (
          <Link key={index} href={item.href} className={cn('group flex flex-col items-center')}>
            <div className="max-h-[516px] max-w-[370px] overflow-hidden">
              <Image
                src={item.imageSrc}
                alt={item.alt}
                width={370}
                height={576}
                className={cn(
                  'hidden md:block rounded-sm transition-transform duration-1000 max-w-[370px] group-hover:scale-110 max-h-[516px]'
                )}
              />
            </div>
            <span className="mt-[30px] text-[25px] underline">{item.label}</span>
          </Link>
        ))}
      </div>

      <div className="mx-auto mt-[90px]">
        <h2 className="text-center text-[30px]">Watch Brands at Laings</h2>
        <div className="mt-[30px] flex gap-x-[30px]">
          {byBrand.map((item, index) => (
            <Link key={index} href={item.href} className={cn('group flex flex-col items-center')}>
              <div className="max-h-[370px] max-w-[370px] overflow-hidden">
                <Image
                  src={item.imageSrc}
                  alt={item.alt}
                  width={370}
                  height={370}
                  className={cn(
                    'hidden md:block rounded-sm transition-transform duration-1000 max-w-[370px] group-hover:scale-110 max-h-[370px]'
                  )}
                />
              </div>
              <span className="mt-[30px] text-[25px] underline">{item.label}</span>
            </Link>
          ))}
        </div>
      </div>

      <div className="mx-auto mt-[90px] max-w-[1170px]">
        <h4 className="mb-5 text-center text-[16px]">At Charles Fox</h4>
        <h2 className="mb-7 text-center text-[30px]">New Watch Arrivals</h2>
        <p className="mb-12 text-center text-[20px]">
          The world of luxury watches is ever-evolving, with innovations constantly being introduced. Laings collection
          of new arrival watches keeps you up to date on the latest innovations and timepieces from world-renowned
          watchmakers. Explore new arrival ladies&apos; watches and new arrival men&apos;s watches and stay up to date
          with what’s new in the world of watches.
        </p>
        <Carousel
          opts={{
            align: 'start',
            loop: true,
          }}
          setApi={setApi}
          plugins={[plugin.current]}
          className="w-full"
        >
          <CarouselContent className="-ml-1 gap-0 md:-ml-2">
            {filteredItems.map((item) => {
              return (
                <CarouselItem key={item.key} className="flex basis-1/2 justify-center pl-1 md:pl-2 lg:basis-1/4">
                  <Link href={item.slug.current} className="w-full"></Link>
                </CarouselItem>
              );
            })}
          </CarouselContent>

          <div className="absolute -bottom-6 left-1/2 flex -translate-x-1/2 items-center gap-1">
            {filteredItems.map((_, index: number) => (
              <div
                key={index}
                className={cn(
                  'h-[3px] rounded-full transition-all duration-300 ease-in-out',
                  index === current - 1 ? 'w-6 bg-rolex-green-600' : 'w-4 bg-gray-300'
                )}
              />
            ))}
          </div>
        </Carousel>
      </div>
    </div>
  );
}
