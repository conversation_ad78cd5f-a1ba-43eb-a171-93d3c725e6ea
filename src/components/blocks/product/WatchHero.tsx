'use client';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { cn } from '@/lib/utils';
import { getSpecificWatchProduct } from '@/sanity/lib/helpers';
import { urlFor } from '@/sanity/lib/image';
import Autoplay from 'embla-carousel-autoplay';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import { useEffect } from 'react';

export default function WatchHero() {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = React.useState(0);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCount] = React.useState(0);
  const [images, setImages] = React.useState<Array<any> | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    const fetchData = async () => {
      const result = await getSpecificWatchProduct(pathname);
      if (result) {
        const images = result?.content?.find((item: any) => item._type === 'imageGridCols').images;
        if (images && images.length > 0) {
          const _arr = result.image ? [{ image: result.image, alt: ' ', _key: ' ' }] : [];
          setImages([..._arr, ...images]);
        }
      }
    };

    fetchData();

    if (!api) {
      return;
    }

    api.on('select', () => {
      setCount(api.scrollSnapList().length);
      setCurrent(api.selectedScrollSnap() + 1);

      api.on('select', () => {
        setCurrent(api.selectedScrollSnap() + 1);
      });
    });
  }, [pathname, api]);

  return (
    <Carousel
      opts={{
        align: 'start',
        loop: true,
      }}
      plugins={[
        Autoplay({
          delay: 5000,
        }),
      ]}
      className={cn('w-full')}
      setApi={setApi}
    >
      <CarouselContent>
        {images?.map((item) => {
          const image = urlFor(item.image.asset);
          const { width, height } = image?.dimensions() || { width: 400, height: 400 };
          return (
            <CarouselItem key={'item_' + item._key} className="flex items-center justify-center">
              <div className="flex max-w-[430px] items-center justify-center">
                <Image src={image?.url() || ''} key={item._key} alt={item.alt} height={height} width={width} />
              </div>
            </CarouselItem>
          );
        })}
      </CarouselContent>

      <div className="absolute -bottom-6 left-1/2 flex -translate-x-1/2 items-center gap-1">
        {images?.map((_, index: number) => (
          <div
            key={index}
            className={cn(
              'h-[3px] rounded-full transition-all duration-300 ease-in-out',
              index === current - 1 ? 'w-6 bg-rolex-green-600' : 'w-4 bg-gray-300'
            )}
          />
        ))}
      </div>
    </Carousel>
  );
}
