'use client';

import {
  NavigationContent,
  NavigationContentJewellery,
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import { NavigationLoop } from '../ui/navigation-loop';
import { jewelleryItems, pageLinks, watchesNavigationData } from './data';

interface PersistentNavigationMenuItemProps {
  trigger: string;
  children: React.ReactNode;
  className?: string;
}

const PersistentNavigationMenuItem: React.FC<PersistentNavigationMenuItemProps> = ({
  trigger,
  children,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const menuRef = useRef<HTMLLIElement>(null);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 150); // Add a small delay before closing
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <NavigationMenuItem
      className={className}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={menuRef}
    >
      <NavigationMenuTrigger
        className="m-0 bg-transparent p-0 text-base font-normal text-foreground-500"
        onClick={(e) => {
          e.preventDefault();
          setIsOpen(true);
        }}
      >
        {trigger}
      </NavigationMenuTrigger>
      {isOpen && (
        <NavigationMenuContent
          className="flex items-center justify-evenly"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {children}
        </NavigationMenuContent>
      )}
    </NavigationMenuItem>
  );
};

const Navigation: React.FC = () => {
  return (
    <NavigationMenu className={cn('mx-auto hidden w-full items-center md:flex relative')} orientation="horizontal">
      <NavigationMenuList className="space-x-16">
        <NavigationLoop items={pageLinks.slice(0, 1)} />

        <PersistentNavigationMenuItem trigger="Watches" className="w-full">
          <NavigationContent data={watchesNavigationData} />
        </PersistentNavigationMenuItem>

        <PersistentNavigationMenuItem trigger="Jewellery" className="w-full">
          <NavigationContentJewellery items={jewelleryItems} />
        </PersistentNavigationMenuItem>

        <NavigationLoop items={pageLinks.slice(1)} />
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default Navigation;
