import HeroCarousel from '@/components/blocks/HeroCarousel';
import RolexContactCard from '@/components/blocks/rolex/RolexContactCard';
import { urlFor } from '@/sanity/lib/image';
import { PageQueryResult } from '@/sanity/types';
import { PortableTextBlock } from '@portabletext/types';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import FinancialPreAppraisal from './blocks/Appraisal';
import { ContactForm } from './blocks/ContactForm';
import FeaturedItems from './blocks/FeaturedItems';
import FullWidthCta from './blocks/FullWidthCta';
import ImageContent, { ImageContentProps } from './blocks/ImageContent';
import { ImageCta } from './blocks/ImageCta';
import ImageGridCols from './blocks/ImageGridCols';
import ProseTitle from './blocks/ProseTitle';
import RichText from './blocks/RichText';
import { VideoData, VideoEmbed } from './blocks/VideoEmbed';
import RolexAccordion from './blocks/rolex/RolexAccordion';
import RolexContactUsAccordion from './blocks/rolex/RolexContactUsAccordion';
import RolexBookingSystem from './blocks/rolex/RolexBookingSystem';
import RolexCard, { RolexCardProps } from './blocks/rolex/RolexCard';
import RolexCardGroup from './blocks/rolex/RolexCardGroup';
import RolexContactForm from './blocks/rolex/RolexContactForm';
import RolexFeatureList from './blocks/rolex/RolexFeatureList';
import RolexHomeBanner from './blocks/rolex/RolexHomeBanner';
import RolexModelHero from './blocks/rolex/RolexModelHero';
import RolexPageTitle from './blocks/rolex/RolexPageTitle';
import RolexPostGroup from './blocks/rolex/RolexPostGroup';
import RolexRichText from './blocks/rolex/RolexRichText';
import RolexRichTextCalibre from './blocks/rolex/RolexRichTextCalibre';
import RolexWatchGroup from './blocks/rolex/RolexWatchGroup';
import RolexProductList from './blocks/rolex/RolexProductList';
import ForDev from './blocks/ForDev';

type PageContent = NonNullable<PageQueryResult>['content'];
type PageComponent = NonNullable<PageContent>[number];

export type RichTextBlockProps = Extract<PageComponent, { _type: 'richText' }>;
export type ImageGridColsBlockProps = Extract<PageComponent, { _type: 'imageGridCols' }>;

type BuilderProps = {
  content: PageContent;
  isRolex?: boolean;
};

/**
 * Builder component that renders a page based on the given `content` array.
 *
 * The `content` array is expected to be an array of objects with a `_type`
 * property that determines which component to render.
 *
 * The `isRolex` prop is used to determine if the page is a Rolex page or not.
 * If `isRolex` is true, the Rolex specific components or alterations to
 * standard components will be rendered.
 *
 * There is a marker comment in the return statement signifying the
 * Rolex specific blocks
 */
const Builder = ({ content, isRolex }: BuilderProps) => {
  return (
    <>
      {content?.map((block, index) => {
        switch (block._type) {
          // Hero Carousel
          case 'heroCarousel':
            return (
              <HeroCarousel
                key={`builder_block_${index}_${block._type}`}
                slides={block.slides || []}
                isRolex={isRolex}
              />
            );

          // Featured Items
          case 'featuredItems':
            return <FeaturedItems key={`builder_block_${index}_${block._type}`} items={block.items || []} />;

          // Base rich text
          case 'richText':
            return <RichText key={`builder_block_${index}_${block._type}`} data={block} />;

          case 'imageGridCols':
            return <ImageGridCols data={block} key={`builder_block_${index}_${block._type}`} />;

          // Image Call to Action
          case 'imageCta':
            return (
              <ImageCta
                key={`builder_block_${index}_${block._type}`}
                imageSrc={urlFor(block.image || { _type: 'image' })?.url() as string}
                imageAlt={block.title as string}
                title={block.title as string}
                description={block.description as string}
                linkHref={block.href as string}
                linkText={block.text as string}
              />
            );

          // Full Width Call to Action
          case 'fullWidthCta':
            return (
              <FullWidthCta
                key={`builder_block_${index}_${block._type}`}
                backgroundImage={urlFor(block.backgroundImage || { _type: 'image' })?.url() as string}
                title={block.title as string}
                description={block.description as string}
                ctaLink={block.ctaLink as string}
                ctaText={block.ctaText as string}
              />
            );

          // Image Content Grid
          case 'imageContent':
            return (
              <ImageContent
                key={`builder_block_${index}_${block._type}`}
                title={block.title as string}
                content={(block.content as PortableTextBlock[]) ?? null}
                dateObjects={block.dateObjects as ImageContentProps['dateObjects']}
                imageUrl={urlFor(block.image || { _type: 'image' })?.url() as string}
                imageAlt={block.imageAlt as string}
                imagePosition={block.imagePosition ?? 'right'}
              />
            );

          // Financial Pre Appraisal
          case 'financialPreAppraisal':
            return (
              <FinancialPreAppraisal
                key={`builder_block_${index}_${block._type}`}
                subtitle={block.subtitle as string}
                retailPrices={block.retailPrices as number[]}
              />
            );

          // Prose Title
          case 'proseTitle':
            return (
              <ProseTitle
                key={`builder_block_${index}_${block._type}`}
                title={block.title as string}
                subtitle={block.subtitle as string}
              />
            );

          // Contact Form
          case 'contactForm': {
            return (
              <ContactForm
                key={`builder_block_${index}_${block._type}`}
                title={block.title as string}
                subtitle={block.subtitle as string}
              />
            );
          }

          // Embedded Video
          case 'video': {
            return <VideoEmbed key={`builder_block_${index}_${block._type}`} video={block as VideoData} />;
          }

          // Rolex Specific Blocks
          case 'rolexPageTitle':
            return (
              <RolexPageTitle
                key={`builder_block_${index}_${block._type}`}
                title={block.title as string}
                subtitle={block.subtitle as PortableTextBlock[]}
              />
            );

          // Rolex Link Card
          case 'rolexCard':
            return (
              <RolexCard
                key={`builder_block_${index}_${block._type}`}
                image={block.image as SanityImageSource}
                mobileImage={block.mobileImage as SanityImageSource}
                title={block.title as string}
                ctaSubheading={block.ctaSubheading as string}
                ctaHeading={block.ctaHeading as string}
                ctaText={block.ctaText as string}
                ctaLink={block.ctaLink as string}
                uiType={block.uiType}
              />
            );

          // Rolex Booking Type
          case 'rolexBookingType':
            return <RolexBookingSystem cards={block.cards} />;

          // Rolex Card Group
          case 'rolexCardGroup':
            return (
              <RolexCardGroup
                key={`builder_block_${index}_${block._type}`}
                title={block.title as string}
                cards={block.cards as RolexCardProps[]}
                uiType={block.uiType}
              />
            );

          // Rolex Rich Text Content
          case 'rolexRichText':
            return (
              <RolexRichText
                key={`builder_block_${index}_${block._type}`}
                value={block.content as PortableTextBlock[]}
                isAlternativeBackgroundColor={block.alternativeBackgroundColor}
              />
            );

          // Rolex Rich Text Content
          case 'rolexRichTextCalibre':
            return (
              <RolexRichTextCalibre
                key={`builder_block_${index}_${block._type}`}
                value={block.content as PortableTextBlock[]}
                isAlternativeBackgroundColor={block.alternativeBackgroundColor}
                modelAvailability={block.modelAvailability}
              />
            );

          // Rolex Contact Card
          case 'rolexContactCard':
            return <RolexContactCard key={`builder_block_${index}_${block._type}`} />;

          // Rolex Home Banner
          case 'rolexHomeBanner':
            return <RolexHomeBanner key={`builder_block_${index}_${block._type}`} />;

          // Rolex Contact Form
          case 'rolexContactForm':
            return <RolexContactForm key={`builder_block_${index}_${block._type}`} />;

          // Rolex Watch Group
          case 'rolexWatchGroup':
            return (
              <RolexWatchGroup
                key={`builder_block_${index}_${block._type}`}
                group={block.title as string}
                uiType={block.uiType}
              />
            );

          // Rolex Post Group
          case 'rolexPostGroup':
            return <RolexPostGroup key={`builder_block_${index}_${block._type}`} />;

          // Rolex Model Hero
          case 'rolexModelHero':
            return (
              <RolexModelHero
                key={`builder_block_${index}_${block._type}`}
                heading={block.heading as string}
                subheading={block.subheading as string}
                body={block.body as PortableTextBlock[]}
                image={block.image as SanityImageSource}
              />
            );

          // Rolex Feature List
          case 'rolexFeatureList':
            return (
              <RolexFeatureList
                key={`builder_block_${index}_${block._type}`}
                features={block.features as Array<{ title: string; description: string }>}
                cta={{
                  link: block.cta?.link ?? '',
                  text: block.cta?.text ?? '',
                }}
              />
            );

          // Rolex Accordion
          case 'rolexAccordionType':
            return block.visible && <RolexAccordion key={`builder_block_${index}_${block._type}`} />;

          // Rolex Accordion
          case 'rolexContactUsAccordionType':
            return block.visible && <RolexContactUsAccordion key={`builder_block_${index}_${block._type}`} />;

          // Rolex Product List
          case 'rolexProductList':
            return (
              <RolexProductList key={`builder_block_${index}_${block._type}`} category={block.category as string} />
            );

          // For Dev
          case 'forDev':
            return <ForDev key={`builder_block_${index}_${block._type}`} />;

          default:
            return null;
        }
      })}
    </>
  );
};

export default Builder;
